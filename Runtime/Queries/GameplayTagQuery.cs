using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using GameplayTags.Core;

namespace GameplayTags.Queries
{
	/// <summary>
	/// Query system for complex GameplayTag matching operations
	/// Similar to UE5's GameplayTagQuery system
	/// </summary>
	[Serializable]
	public class GameplayTagQuery
	{
		[SerializeField] private QueryType queryType = QueryType.HasAnyTags;
		[SerializeField] private GameplayTagContainer requiredTags = new();
		[SerializeField] private GameplayTagContainer forbiddenTags = new();
		[SerializeField] private List<GameplayTagQuery> subQueries = new();
		[SerializeField] private LogicalOperator logicalOperator = LogicalOperator.And;

		public enum QueryType
		{
			HasAnyTags, // Container must have at least one of the required tags
			HasAllTags, // Container must have all of the required tags
			HasNoTags, // Container must not have any of the forbidden tags
			Complex // Use sub-queries with logical operators
		}

		public enum LogicalOperator
		{
			And,
			Or,
			Not
		}

		public GameplayTagQuery()
		{
		}

		public GameplayTagQuery(QueryType type)
		{
			queryType = type;
		}

		/// <summary>
		/// Creates a query that requires any of the specified tags
		/// </summary>
		public static GameplayTagQuery RequireAnyTags(params GameplayTag[] tags)
		{
			var query = new GameplayTagQuery(QueryType.HasAnyTags);
			foreach (var tag in tags)
				query.requiredTags.AddTag(tag, "Query");
			return query;
		}

		/// <summary>
		/// Creates a query that requires all of the specified tags
		/// </summary>
		public static GameplayTagQuery RequireAllTags(params GameplayTag[] tags)
		{
			var query = new GameplayTagQuery(QueryType.HasAllTags);
			foreach (var tag in tags)
				query.requiredTags.AddTag(tag, "Query");
			return query;
		}

		/// <summary>
		/// Creates a query that forbids any of the specified tags
		/// </summary>
		public static GameplayTagQuery ForbidTags(params GameplayTag[] tags)
		{
			var query = new GameplayTagQuery(QueryType.HasNoTags);
			foreach (var tag in tags)
				query.forbiddenTags.AddTag(tag, "Query");
			return query;
		}

		/// <summary>
		/// Creates a complex query with sub-queries
		/// </summary>
		public static GameplayTagQuery Complex(LogicalOperator op, params GameplayTagQuery[] queries)
		{
			var query = new GameplayTagQuery(QueryType.Complex)
			{
				logicalOperator = op
			};
			
			query.subQueries.AddRange(queries);
			return query;
		}

		/// <summary>
		/// Evaluates the query against a GameplayTagContainer
		/// </summary>
		public bool Matches(GameplayTagContainer container)
		{
			switch (queryType)
			{
				case QueryType.HasAnyTags:
					return requiredTags.IsEmpty || container.HasAnyTags(requiredTags);

				case QueryType.HasAllTags:
					return requiredTags.IsEmpty || container.HasAllTags(requiredTags);

				case QueryType.HasNoTags:
					return forbiddenTags.IsEmpty || !container.HasAnyTags(forbiddenTags);

				case QueryType.Complex:
					return EvaluateComplexQuery(container);

				default:
					return false;
			}
		}

		private bool EvaluateComplexQuery(GameplayTagContainer container)
		{
			if (subQueries.Count == 0) return true;

			switch (logicalOperator)
			{
				case LogicalOperator.And:
					return subQueries.All(q => q.Matches(container));

				case LogicalOperator.Or:
					return subQueries.Any(q => q.Matches(container));

				case LogicalOperator.Not:
					// For NOT, we negate the result of the first sub-query
					return subQueries.Count > 0 && !subQueries[0].Matches(container);

				default:
					return false;
			}
		}

		/// <summary>
		/// Adds a required tag to this query
		/// </summary>
		public GameplayTagQuery AddRequiredTag(GameplayTag tag)
		{
			requiredTags.AddTag(tag, "Query");
			return this;
		}

		/// <summary>
		/// Adds a forbidden tag to this query
		/// </summary>
		public GameplayTagQuery AddForbiddenTag(GameplayTag tag)
		{
			forbiddenTags.AddTag(tag, "Query");
			return this;
		}

		/// <summary>
		/// Adds a sub-query
		/// </summary>
		public GameplayTagQuery AddSubQuery(GameplayTagQuery subQuery)
		{
			queryType = QueryType.Complex;
			subQueries.Add(subQuery);
			return this;
		}

		public override string ToString()
		{
			return queryType switch
			{
				QueryType.HasAnyTags => $"HasAnyTags({string.Join(", ", requiredTags)})", 
				QueryType.HasAllTags => $"HasAllTags({string.Join(", ", requiredTags)})", 
				QueryType.HasNoTags => $"HasNoTags({string.Join(", ", forbiddenTags)})", 
				QueryType.Complex => $"Complex({logicalOperator}: {string.Join($" {logicalOperator} ", subQueries)})", 
				_ => "EmptyQuery"
			};
		}
	}

	/// <summary>
	/// Extension methods for GameplayTagContainer to work with queries
	/// </summary>
	public static class GameplayTagQueryExtensions
	{
		/// <summary>
		/// Checks if a container matches a query
		/// </summary>
		public static bool MatchesQuery(this GameplayTagContainer container, GameplayTagQuery query)
		{
			return query.Matches(container);
		}

		/// <summary>
		/// Checks if a container matches any of the provided queries
		/// </summary>
		public static bool MatchesAnyQuery(this GameplayTagContainer container, params GameplayTagQuery[] queries)
		{
			return queries.Any(query => query.Matches(container));
		}

		/// <summary>
		/// Checks if a container matches all of the provided queries
		/// </summary>
		public static bool MatchesAllQueries(this GameplayTagContainer container, params GameplayTagQuery[] queries)
		{
			return queries.All(query => query.Matches(container));
		}
	}
}
