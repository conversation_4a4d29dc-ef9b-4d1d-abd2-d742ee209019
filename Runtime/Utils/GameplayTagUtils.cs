using System.Collections.Generic;
using UnityEngine;
using GameplayTags.Core;
using GameplayTags.Components;
using GameplayTags.Queries;

namespace GameplayTags.Utils
{
    /// <summary>
    /// Utility class providing common GameplayTag operations and helpers
    /// </summary>
    public static class GameplayTagUtils
    {
        /// <summary>
        /// Finds all GameplayTagComponents in the scene
        /// </summary>
        public static GameplayTagComponent[] FindAllTaggedObjects()
        {
            return Object.FindObjectsByType<GameplayTagComponent>(FindObjectsSortMode.None);
        }
        
        /// <summary>
        /// Finds all objects with a specific tag
        /// </summary>
        public static GameplayTagComponent[] FindObjectsWithTag(GameplayTag tag)
        {
            var allTagged = FindAllTaggedObjects();
            return System.Array.FindAll(allTagged, obj => obj.HasTag(tag));
        }
        
        /// <summary>
        /// Finds all objects matching a query
        /// </summary>
        public static GameplayTagComponent[] FindObjectsMatchingQuery(GameplayTagQuery query)
        {
            var allTagged = FindAllTaggedObjects();
            return System.Array.FindAll(allTagged, obj => obj.GameplayTags.MatchesQuery(query));
        }
        
        /// <summary>
        /// Gets the distance between two tagged objects
        /// </summary>
        public static float GetDistance(GameplayTagComponent obj1, GameplayTagComponent obj2)
        {
            if (obj1 == null || obj2 == null) return float.MaxValue;
            return Vector3.Distance(obj1.transform.position, obj2.transform.position);
        }
        
        /// <summary>
        /// Finds the closest object with a specific tag
        /// </summary>
        public static GameplayTagComponent FindClosestObjectWithTag(Vector3 position, GameplayTag tag)
        {
            var objectsWithTag = FindObjectsWithTag(tag);
            GameplayTagComponent closest = null;
            var closestDistance = float.MaxValue;
            
            foreach (var obj in objectsWithTag)
            {
                var distance = Vector3.Distance(position, obj.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closest = obj;
                }
            }
            
            return closest;
        }
        
        /// <summary>
        /// Finds all objects within a specified radius that have a specific tag
        /// </summary>
        public static GameplayTagComponent[] FindObjectsWithTagInRadius(Vector3 center, float radius, GameplayTag tag)
        {
            var objectsWithTag = FindObjectsWithTag(tag);
            return System.Array.FindAll(objectsWithTag, obj => 
                Vector3.Distance(center, obj.transform.position) <= radius);
        }
        
        /// <summary>
        /// Finds all objects within a specified radius that match a query
        /// </summary>
        public static GameplayTagComponent[] FindObjectsMatchingQueryInRadius(Vector3 center, float radius, GameplayTagQuery query)
        {
            var objectsMatchingQuery = FindObjectsMatchingQuery(query);
            return System.Array.FindAll(objectsMatchingQuery, obj => 
                Vector3.Distance(center, obj.transform.position) <= radius);
        }
        
        /// <summary>
        /// Gets all objects with tags sorted by distance from a position
        /// </summary>
        public static GameplayTagComponent[] GetObjectsSortedByDistance(Vector3 position, GameplayTag tag)
        {
            var objectsWithTag = FindObjectsWithTag(tag);
            System.Array.Sort(objectsWithTag, (a, b) => 
                Vector3.Distance(position, a.transform.position).CompareTo(
                Vector3.Distance(position, b.transform.position)));
            return objectsWithTag;
        }
        
        /// <summary>
        /// Finds the first object with any of the specified tags
        /// </summary>
        public static GameplayTagComponent FindFirstObjectWithAnyTag(params GameplayTag[] tags)
        {
            var allTagged = FindAllTaggedObjects();
            foreach (var obj in allTagged)
            {
                foreach (var tag in tags)
                {
                    if (obj.HasTag(tag))
                        return obj;
                }
            }
            return null;
        }
        
        /// <summary>
        /// Finds all objects that have ALL of the specified tags
        /// </summary>
        public static GameplayTagComponent[] FindObjectsWithAllTags(params GameplayTag[] tags)
        {
            var allTagged = FindAllTaggedObjects();
            return System.Array.FindAll(allTagged, obj => 
            {
                foreach (var tag in tags)
                {
                    if (!obj.HasTag(tag))
                        return false;
                }
                return true;
            });
        }
        
        /// <summary>
        /// Counts how many objects have a specific tag
        /// </summary>
        public static int CountObjectsWithTag(GameplayTag tag)
        {
            return FindObjectsWithTag(tag).Length;
        }
        
        /// <summary>
        /// Gets all unique tags currently present in the scene
        /// </summary>
        public static GameplayTagContainer GetAllTagsInScene()
        {
            var allTagged = FindAllTaggedObjects();
            var uniqueTags = new GameplayTagContainer();
            
            foreach (var obj in allTagged)
            {
                foreach (var tagStack in obj.GameplayTags)
                {
                    uniqueTags.AddTag(tagStack.Tag, "ObjectTag");
                }
            }
            
            return uniqueTags;
        }
        
        /// <summary>
        /// Filters objects by layer in addition to tags
        /// </summary>
        public static GameplayTagComponent[] FindObjectsWithTagOnLayer(GameplayTag tag, int layer)
        {
            var objectsWithTag = FindObjectsWithTag(tag);
            return System.Array.FindAll(objectsWithTag, obj => obj.gameObject.layer == layer);
        }
        
        /// <summary>
        /// Finds objects within a sphere that excludes a specific object (useful for AI behaviors)
        /// </summary>
        public static GameplayTagComponent[] FindNearbyObjectsExcluding(Vector3 center, float radius, 
            GameplayTag tag, GameObject exclude)
        {
            var nearby = FindObjectsWithTagInRadius(center, radius, tag);
            return System.Array.FindAll(nearby, obj => obj.gameObject != exclude);
        }
        
        /// <summary>
        /// Checks if there are any objects with the specified tag within range
        /// </summary>
        public static bool AnyObjectsWithTagInRange(Vector3 center, float radius, GameplayTag tag)
        {
            var objectsWithTag = FindObjectsWithTag(tag);
            foreach (var obj in objectsWithTag)
            {
                if (Vector3.Distance(center, obj.transform.position) <= radius)
                    return true;
            }
            return false;
        }
        
        /// <summary>
        /// Gets the average position of all objects with a specific tag
        /// </summary>
        public static Vector3 GetAveragePositionOfObjectsWithTag(GameplayTag tag)
        {
            var objectsWithTag = FindObjectsWithTag(tag);
            if (objectsWithTag.Length == 0)
                return Vector3.zero;
                
            var sum = Vector3.zero;
            foreach (var obj in objectsWithTag)
            {
                sum += obj.transform.position;
            }
            return sum / objectsWithTag.Length;
        }
        
        /// <summary>
        /// Finds the object with a tag that's closest to a line (useful for projectiles, raycast targeting)
        /// </summary>
        public static GameplayTagComponent FindClosestObjectToLine(Vector3 lineStart, Vector3 lineEnd, GameplayTag tag)
        {
            var objectsWithTag = FindObjectsWithTag(tag);
            GameplayTagComponent closest = null;
            var closestDistance = float.MaxValue;
            
            foreach (var obj in objectsWithTag)
            {
                var closestPoint = ClosestPointOnLine(lineStart, lineEnd, obj.transform.position);
                var distance = Vector3.Distance(closestPoint, obj.transform.position);
                
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closest = obj;
                }
            }
            
            return closest;
        }
        
        /// <summary>
        /// Helper method to find closest point on a line segment
        /// </summary>
        private static Vector3 ClosestPointOnLine(Vector3 lineStart, Vector3 lineEnd, Vector3 point)
        {
            var lineDirection = lineEnd - lineStart;
            var lineLength = lineDirection.magnitude;
            lineDirection.Normalize();
            
            var toPoint = point - lineStart;
            var projectionLength = Vector3.Dot(toPoint, lineDirection);
            projectionLength = Mathf.Clamp(projectionLength, 0f, lineLength);
            
            return lineStart + lineDirection * projectionLength;
        }
        
        /// <summary>
        /// Performance utilities for monitoring tag system usage
        /// </summary>
        public static class Performance
        {
            /// <summary>
            /// Gets performance stats about tagged objects in the scene
            /// </summary>
            public static TagPerformanceStats GetSceneTagStats()
            {
                var allTagged = FindAllTaggedObjects();
                var stats = new TagPerformanceStats
                {
                    TotalTaggedObjects = allTagged.Length,
                    TotalTags = 0,
                    AverageTagsPerObject = 0f,
                    MostTagsOnSingleObject = 0
                };
                
                var totalTags = 0;
                foreach (var obj in allTagged)
                {
                    var objTagCount = obj.GameplayTags.Count;
                    totalTags += objTagCount;
                    if (objTagCount > stats.MostTagsOnSingleObject)
                        stats.MostTagsOnSingleObject = objTagCount;
                }
                
                stats.TotalTags = totalTags;
                stats.AverageTagsPerObject = allTagged.Length > 0 ? (float)totalTags / allTagged.Length : 0f;
                
                return stats;
            }
        }
        
        /// <summary>
        /// Data structure for performance statistics
        /// </summary>
        [System.Serializable]
        public struct TagPerformanceStats
        {
            public int TotalTaggedObjects;
            public int TotalTags;
            public float AverageTagsPerObject;
            public int MostTagsOnSingleObject;
            
            public override string ToString()
            {
                return $"Tagged Objects: {TotalTaggedObjects}, Total Tags: {TotalTags}, " +
                       $"Avg Tags/Object: {AverageTagsPerObject:F2}, Max Tags on Object: {MostTagsOnSingleObject}";
            }
        }
        
        /// <summary>
        /// Debug utilities for testing and development
        /// </summary>
        public static class Debug
        {
            [System.Diagnostics.Conditional("UNITY_EDITOR")]
            public static void LogTagContainer(GameplayTagContainer container, string prefix = "")
            {
                if (container.IsEmpty)
                {
                    UnityEngine.Debug.Log($"{prefix}Container is empty");
                }
                else
                {
                    UnityEngine.Debug.Log($"{prefix}Container ({container.Count} tags): {string.Join(", ", container)}");
                }
            }
            
            [System.Diagnostics.Conditional("UNITY_EDITOR")]
            public static void LogTagQuery(GameplayTagQuery query, GameplayTagContainer testContainer)
            {
                var matches = testContainer.MatchesQuery(query);
                UnityEngine.Debug.Log($"Query: {query} | Matches: {matches} | Container: {string.Join(", ", testContainer)}");
            }
            
            public static void ValidateTaggedObjectsInScene()
            {
                var allTagged = FindAllTaggedObjects();
                UnityEngine.Debug.Log($"Found {allTagged.Length} objects with GameplayTagComponent in scene:");
                
                foreach (var obj in allTagged)
                {
                    UnityEngine.Debug.Log($"  {obj.name}: {obj.GameplayTags.Count} tags - {string.Join(", ", obj.GameplayTags)}");
                }
            }
            
            [System.Diagnostics.Conditional("UNITY_EDITOR")]
            public static void DrawTaggedObjectsGizmos(GameplayTag tag, Color color)
            {
                var objects = FindObjectsWithTag(tag);
                foreach (var obj in objects)
                {
                    Gizmos.color = color;
                    Gizmos.DrawWireSphere(obj.transform.position, 1f);
                }
            }
            
            [System.Diagnostics.Conditional("UNITY_EDITOR")]
            public static void LogPerformanceStats()
            {
                var stats = Performance.GetSceneTagStats();
                UnityEngine.Debug.Log($"Tag Performance Stats: {stats}");
            }
        }
        
        /// <summary>
        /// Gameplay-specific utilities for common game mechanics
        /// </summary>
        public static class Gameplay
        {
            /// <summary>
            /// Finds potential targets for abilities or spells within range
            /// </summary>
            public static GameplayTagComponent[] FindTargetsForAbility(Vector3 casterPosition, float range, 
                GameplayTag targetTag, GameplayTag[] excludeTags = null)
            {
                var potentialTargets = FindObjectsWithTagInRadius(casterPosition, range, targetTag);
                
                if (excludeTags != null && excludeTags.Length > 0)
                {
                    potentialTargets = System.Array.FindAll(potentialTargets, target =>
                    {
                        foreach (var excludeTag in excludeTags)
                        {
                            if (target.HasTag(excludeTag))
                                return false;
                        }
                        return true;
                    });
                }
                
                return potentialTargets;
            }
            
            /// <summary>
            /// Finds the best cover point near a position (objects with cover tag)
            /// </summary>
            public static GameplayTagComponent FindBestCover(Vector3 fromPosition, Vector3 threatPosition, 
                GameplayTag coverTag, float searchRadius = 10f)
            {
                var coverObjects = FindObjectsWithTagInRadius(fromPosition, searchRadius, coverTag);
                GameplayTagComponent bestCover = null;
                var bestScore = float.MinValue;
                
                foreach (var cover in coverObjects)
                {
                    // Score based on distance from threat and distance from seeker
                    var distanceFromThreat = Vector3.Distance(cover.transform.position, threatPosition);
                    var distanceFromSeeker = Vector3.Distance(cover.transform.position, fromPosition);
                    
                    // Prefer cover that's further from threat but closer to seeker
                    var score = distanceFromThreat - (distanceFromSeeker * 0.5f);
                    
                    if (score > bestScore)
                    {
                        bestScore = score;
                        bestCover = cover;
                    }
                }
                
                return bestCover;
            }
            
            /// <summary>
            /// Groups objects by their tag hierarchy level (useful for organizing UI or inventory)
            /// </summary>
            public static Dictionary<string, List<GameplayTagComponent>> GroupObjectsByTagCategory(GameplayTag categoryTag)
            {
                var result = new Dictionary<string, List<GameplayTagComponent>>();
                var allTagged = FindAllTaggedObjects();
                
                foreach (var obj in allTagged)
                {
                    foreach (var tagStack in obj.GameplayTags)
                    {
                        if (tagStack.Tag.TagName.StartsWith(categoryTag.TagName))
                        {
                            var parts = tagStack.Tag.TagName.Split('.');
                            var category = parts.Length > 1 ? parts[1] : "Uncategorized";
                            
                            if (!result.ContainsKey(category))
                                result[category] = new List<GameplayTagComponent>();
                                
                            result[category].Add(obj);
                        }
                    }
                }
                
                return result;
            }
            
            /// <summary>
            /// Finds patrol points in order (useful for AI pathfinding)
            /// </summary>
            public static GameplayTagComponent[] GetPatrolRoute(GameplayTag patrolTag)
            {
                var patrolPoints = FindObjectsWithTag(patrolTag);
                
                // Sort by name to maintain consistent order
                System.Array.Sort(patrolPoints, (a, b) => string.Compare(a.name, b.name));
                
                return patrolPoints;
            }
            
            /// <summary>
            /// Finds spawn points that are not currently occupied
            /// </summary>
            public static GameplayTagComponent[] GetAvailableSpawnPoints(GameplayTag spawnTag, 
                GameplayTag occupiedTag, float occupationRadius = 2f)
            {
                var spawnPoints = FindObjectsWithTag(spawnTag);
                var occupiedObjects = FindObjectsWithTag(occupiedTag);
                
                return System.Array.FindAll(spawnPoints, spawn =>
                {
                    foreach (var occupied in occupiedObjects)
                    {
                        if (Vector3.Distance(spawn.transform.position, occupied.transform.position) < occupationRadius)
                            return false;
                    }
                    return true;
                });
            }
        }
        
        /// <summary>
        /// Collection utilities for managing groups of tagged objects
        /// </summary>
        public static class Collections
        {
            /// <summary>
            /// Creates a cached collection that updates when objects are added/removed
            /// </summary>
            public static TaggedObjectCollection CreateCollection(GameplayTag tag)
            {
                return new TaggedObjectCollection(tag);
            }
            
            /// <summary>
            /// Gets objects as a dictionary keyed by their GameObject name
            /// </summary>
            public static Dictionary<string, GameplayTagComponent> GetObjectsDictionary(GameplayTag tag)
            {
                var objects = FindObjectsWithTag(tag);
                var dict = new Dictionary<string, GameplayTagComponent>();
                
                foreach (var obj in objects)
                {
                    if (!dict.ContainsKey(obj.name))
                        dict[obj.name] = obj;
                }
                
                return dict;
            }
        }
        
        /// <summary>
        /// A cached collection of objects with a specific tag that updates automatically
        /// </summary>
        public class TaggedObjectCollection
        {
            private GameplayTag _tag;
            private GameplayTagComponent[] _cachedObjects;
            private float _lastUpdateTime;
            private float _updateInterval = 1f; // Update every second by default
            
            public GameplayTagComponent[] Objects 
            { 
                get 
                { 
                    RefreshIfNeeded(); 
                    return _cachedObjects; 
                } 
            }
            
            public int Count => Objects.Length;
            public float UpdateInterval { get => _updateInterval; set => _updateInterval = value; }
            
            public TaggedObjectCollection(GameplayTag tag)
            {
                _tag = tag;
                Refresh();
            }
            
            public void Refresh()
            {
                _cachedObjects = FindObjectsWithTag(_tag);
                _lastUpdateTime = Time.time;
            }
            
            private void RefreshIfNeeded()
            {
                if (Time.time - _lastUpdateTime > _updateInterval)
                {
                    Refresh();
                }
            }
            
            public GameplayTagComponent GetClosest(Vector3 position)
            {
                return FindClosestObjectWithTag(position, _tag);
            }
            
            public GameplayTagComponent[] GetInRadius(Vector3 center, float radius)
            {
                return FindObjectsWithTagInRadius(center, radius, _tag);
            }
        }
    }
}
