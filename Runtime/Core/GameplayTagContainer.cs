using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using PurrNet.Packing;

namespace GameplayTags.Core
{
    /// <summary>
    /// Container for holding multiple GameplayTags with support for tag stacking from different sources.
    /// Each tag can appear multiple times from different sources (e.g., equipment slots, abilities, etc.)
    /// </summary>
    [Serializable]
    public class GameplayTagContainer : IEnumerable<TagStack>, IPackedAuto
    {
        [SerializeField] private List<TagStack> tagStacks;
        [SerializeField] private Dictionary<GameplayTag, List<TagStack>> tagGroups;

        public int Count => tagStacks.Count;
        public bool IsEmpty => tagStacks.Count == 0;
        
        /// <summary>
        /// Gets the number of unique tags (ignoring duplicates from different sources)
        /// </summary>
        public int UniqueTagCount => tagGroups.Count;

        public GameplayTagContainer()
        {
            tagStacks = new List<TagStack>();
            tagGroups = new Dictionary<GameplayTag, List<TagStack>>();
        }

        /// <summary>
        /// Creates a container from tag stacks
        /// </summary>
        public GameplayTagContainer(IEnumerable<TagStack> initialTagStacks) : this()
        {
            AddTagStacks(initialTagStacks);
        }

        /// <summary>
        /// Creates a container from tag stacks
        /// </summary>
        public GameplayTagContainer(params TagStack[] initialTagStacks) : this()
        {
            AddTagStacks(initialTagStacks);
        }

        /// <summary>
        /// Adds a tag stack to the container
        /// </summary>
        public bool AddTagStack(TagStack tagStack)
        {
            if (!tagStack.IsValid) return false;
            
            // Check if this exact stack already exists
            if (tagStacks.Contains(tagStack)) return false;
            
            tagStacks.Add(tagStack);
            
            // Update tag groups
            if (!tagGroups.ContainsKey(tagStack.Tag))
            {
                tagGroups[tagStack.Tag] = new List<TagStack>();
            }
            tagGroups[tagStack.Tag].Add(tagStack);
            
            return true;
        }

        /// <summary>
        /// Adds a tag with a specific source
        /// </summary>
        public bool AddTag(GameplayTag tag, TagSource source)
        {
            return AddTagStack(new TagStack(tag, source));
        }

        /// <summary>
        /// Adds a tag with a specific source name
        /// </summary>
        public bool AddTag(GameplayTag tag, string sourceName)
        {
            return AddTagStack(new TagStack(tag, sourceName));
        }

        /// <summary>
        /// Adds multiple tag stacks to the container
        /// </summary>
        public void AddTagStacks(IEnumerable<TagStack> tagStacksToAdd)
        {
            foreach (var tagStack in tagStacksToAdd)
            {
                AddTagStack(tagStack);
            }
        }

        /// <summary>
        /// Removes a specific tag stack from the container
        /// </summary>
        public bool RemoveTagStack(TagStack tagStack)
        {
            if (!tagStacks.Remove(tagStack)) return false;
            
            // Update tag groups
            if (tagGroups.ContainsKey(tagStack.Tag))
            {
                tagGroups[tagStack.Tag].Remove(tagStack);
                if (tagGroups[tagStack.Tag].Count == 0)
                {
                    tagGroups.Remove(tagStack.Tag);
                }
            }
            
            return true;
        }

        /// <summary>
        /// Removes a tag from a specific source
        /// </summary>
        public bool RemoveTag(GameplayTag tag, TagSource source)
        {
            return RemoveTagStack(new TagStack(tag, source));
        }

        /// <summary>
        /// Removes all instances of a tag from all sources
        /// </summary>
        public bool RemoveAllInstancesOfTag(GameplayTag tag)
        {
            if (!tagGroups.TryGetValue(tag, out var g)) return false;
            
            var stacksToRemove = g.ToList();
            foreach (var stack in stacksToRemove)
            {
                tagStacks.Remove(stack);
            }
            
            tagGroups.Remove(tag);
            return true;
        }

        /// <summary>
        /// Removes all tag stacks from a specific source
        /// </summary>
        public int RemoveTagsFromSource(TagSource source)
        {
            var stacksToRemove = tagStacks.Where(stack => stack.Source.Equals(source)).ToList();
            var removedCount = 0;
            
            foreach (var stack in stacksToRemove)
            {
                if (RemoveTagStack(stack))
                    removedCount++;
            }
            
            return removedCount;
        }

        /// <summary>
        /// Removes multiple tag stacks from the container
        /// </summary>
        public void RemoveTagStacks(IEnumerable<TagStack> tagStacksToRemove)
        {
            foreach (var tagStack in tagStacksToRemove)
            {
                RemoveTagStack(tagStack);
            }
        }

        /// <summary>
        /// Clears all tag stacks from the container
        /// </summary>
        public void Clear()
        {
            tagStacks.Clear();
            tagGroups.Clear();
        }

        /// <summary>
        /// Checks if the container has a specific tag stack (exact match including source)
        /// </summary>
        public bool HasTagStackExact(TagStack tagStack)
        {
            return tagStacks.Contains(tagStack);
        }

        /// <summary>
        /// Checks if the container has a specific tag from a specific source
        /// </summary>
        public bool HasTagFromSource(GameplayTag tag, TagSource source)
        {
            return HasTagStackExact(new TagStack(tag, source));
        }

        /// <summary>
        /// Checks if the container has a specific tag (exact match, ignoring source)
        /// </summary>
        public bool HasTagExact(GameplayTag tag)
        {
            return tagGroups.ContainsKey(tag);
        }

        /// <summary>
        /// Checks if the container has any tag that matches the given tag (including parent/child relationships)
        /// </summary>
        public bool HasTag(GameplayTag tag)
        {
            return tag.IsValid && tagStacks.Any(stack => stack.MatchesTag(tag) || tag.MatchesTag(stack.Tag));
        }

        /// <summary>
        /// Gets the number of stacks for a specific tag
        /// </summary>
        public int GetTagStackCount(GameplayTag tag)
        {
            return tagGroups.TryGetValue(tag, out var g) ? g.Count : 0;
        }

        /// <summary>
        /// Gets all sources that provide a specific tag
        /// </summary>
        public IEnumerable<TagSource> GetSourcesForTag(GameplayTag tag)
        {
            return !tagGroups.TryGetValue(tag, out var g) ? Enumerable.Empty<TagSource>() : g.Select(stack => stack.Source);
        }

        /// <summary>
        /// Gets all tag stacks for a specific tag
        /// </summary>
        public IEnumerable<TagStack> GetStacksForTag(GameplayTag tag)
        {
            return !tagGroups.TryGetValue(tag, out var g) ? Enumerable.Empty<TagStack>() : g;
        }

        /// <summary>
        /// Checks if the container has all of the specified tags (ignoring sources)
        /// </summary>
        public bool HasAllTags(GameplayTagContainer other)
        {
            return other.GetUniqueTags().All(HasTag);
        }

        /// <summary>
        /// Checks if the container has any of the specified tags (ignoring sources)
        /// </summary>
        public bool HasAnyTags(GameplayTagContainer other)
        {
            return other.GetUniqueTags().Any(HasTag);
        }

        /// <summary>
        /// Gets all unique tags (without duplicates from different sources)
        /// </summary>
        public IEnumerable<GameplayTag> GetUniqueTags()
        {
            return tagGroups.Keys;
        }

        /// <summary>
        /// Gets all tag stacks that match a specific tag (including hierarchical matches)
        /// </summary>
        public IEnumerable<TagStack> GetTagStacksMatching(GameplayTag tag)
        {
            return tagStacks.Where(stack => stack.MatchesTag(tag) || tag.MatchesTag(stack.Tag));
        }

        /// <summary>
        /// Gets all tags that match a specific tag (including hierarchical matches)
        /// </summary>
        public IEnumerable<GameplayTag> GetTagsMatching(GameplayTag tag)
        {
            return GetTagStacksMatching(tag).Select(stack => stack.Tag).Distinct();
        }

        /// <summary>
        /// Filters tag stacks by a predicate
        /// </summary>
        public IEnumerable<TagStack> FilterStacks(Func<TagStack, bool> predicate)
        {
            return tagStacks.Where(predicate);
        }

        /// <summary>
        /// Filters tags by a predicate (returns unique tags)
        /// </summary>
        public IEnumerable<GameplayTag> Filter(Func<GameplayTag, bool> predicate)
        {
            return GetUniqueTags().Where(predicate);
        }

        /// <summary>
        /// Gets all tag stacks with a specific parent tag
        /// </summary>
        public IEnumerable<TagStack> GetTagStacksWithParent(GameplayTag parentTag)
        {
            return tagStacks.Where(stack => parentTag.MatchesTag(stack.Tag));
        }

        /// <summary>
        /// Gets all tags with a specific parent tag (returns unique tags)
        /// </summary>
        public IEnumerable<GameplayTag> GetTagsWithParent(GameplayTag parentTag)
        {
            return GetTagStacksWithParent(parentTag).Select(stack => stack.Tag).Distinct();
        }

        /// <summary>
        /// Gets all tag stacks from a specific source
        /// </summary>
        public IEnumerable<TagStack> GetTagStacksFromSource(TagSource source)
        {
            return tagStacks.Where(stack => stack.Source.Equals(source));
        }

        /// <summary>
        /// Creates a new container with the union of this and another container (combines all tag stacks)
        /// </summary>
        public GameplayTagContainer Union(GameplayTagContainer other)
        {
            var result = new GameplayTagContainer();
            result.AddTagStacks(tagStacks);
            result.AddTagStacks(other.tagStacks);
            return result;
        }

        /// <summary>
        /// Creates a new container with the intersection of this and another container (only tags present in both, keeps all sources)
        /// </summary>
        public GameplayTagContainer Intersection(GameplayTagContainer other)
        {
            var result = new GameplayTagContainer();
            var otherUniqueTags = other.GetUniqueTags().ToHashSet();
            
            result.AddTagStacks(tagStacks.Where(stack => otherUniqueTags.Any(tag => stack.MatchesTag(tag))));
            return result;
        }

        /// <summary>
        /// Creates a new container with tags from this container that are not in the other (by tag, ignoring sources)
        /// </summary>
        public GameplayTagContainer Difference(GameplayTagContainer other)
        {
            var result = new GameplayTagContainer();
            result.AddTagStacks(tagStacks.Where(stack => !other.HasTag(stack.Tag)));
            return result;
        }

        /// <summary>
        /// Creates a new container with unique tags only (removes duplicates, keeping the first occurrence of each tag)
        /// </summary>
        public GameplayTagContainer GetUniqueTagsContainer()
        {
            var result = new GameplayTagContainer();
            var seenTags = new HashSet<GameplayTag>();
            
            foreach (var stack in tagStacks)
            {
                if (seenTags.Add(stack.Tag))
                {
                    result.AddTagStack(stack);
                }
            }
            
            return result;
        }

        /// <summary>
        /// Creates a copy of this container
        /// </summary>
        public GameplayTagContainer Clone()
        {
            return new GameplayTagContainer(tagStacks);
        }

        /// <summary>
        /// Gets all tag stacks as an array
        /// </summary>
        public TagStack[] ToArray()
        {
            return tagStacks.ToArray();
        }

        /// <summary>
        /// Gets all unique tags as an array
        /// </summary>
        public GameplayTag[] GetUniqueTagArray()
        {
            return GetUniqueTags().ToArray();
        }

        public IEnumerator<TagStack> GetEnumerator()
        {
            return tagStacks.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        public override string ToString()
        {
            return $"GameplayTagContainer({tagStacks.Count} stacks, {UniqueTagCount} unique tags: {string.Join(", ", tagStacks)})";
        }

        // Operators for convenience
        public static GameplayTagContainer operator +(GameplayTagContainer left, GameplayTagContainer right)
        {
            return left.Union(right);
        }

        public static GameplayTagContainer operator -(GameplayTagContainer left, GameplayTagContainer right)
        {
            return left.Difference(right);
        }

        // Custom serialization to maintain internal structures
        public void Serialize(BitPacker packer)
        {
            Packer<int>.Write(packer, tagStacks.Count);
            foreach (var tagStack in tagStacks)
            {
                Packer<TagStack>.Write(packer, tagStack);
            }
        }

        public void Deserialize(BitPacker packer)
        {
            Clear();
            var count = 0;
            Packer<int>.Read(packer, ref count);
            for (var i = 0; i < count; i++)
            {
                TagStack tagStack = default;
                Packer<TagStack>.Read(packer, ref tagStack);
                AddTagStack(tagStack);
            }
        }

        /// <summary>
        /// Rebuilds internal data structures. Called automatically after deserialization.
        /// </summary>
        private void RebuildTagGroups()
        {
            tagGroups.Clear();
            foreach (var tagStack in tagStacks)
            {
                if (!tagGroups.ContainsKey(tagStack.Tag))
                {
                    tagGroups[tagStack.Tag] = new List<TagStack>();
                }
                tagGroups[tagStack.Tag].Add(tagStack);
            }
        }
    }
}
