using System;
using System.Collections.Generic;
using UnityEngine;
using PurrNet.Packing;

namespace GameplayTags.Core
{
    /// <summary>
    /// A hierarchical tag system similar to Unreal Engine's GameplayTags.
    /// Tags are represented as dot-separated strings (e.g., "Ability.Fire.Fireball")
    /// and support inheritance matching.
    /// </summary>
    [Serializable]
    public struct GameplayTag : IEquatable<GameplayTag>, IComparable<GameplayTag>, IPackedAuto
    {
        [SerializeField] private string tagName;
        [SerializeField] private int cachedHash;

        // Cache for GameplayTag instances
        private static readonly Dictionary<string, GameplayTag> TagCache = new();
        
        public static readonly GameplayTag None = new();

        /// <summary>
        /// Gets the full tag name (e.g., "Ability.Fire.Fireball")
        /// </summary>
        public string TagName => tagName ?? string.Empty;

        /// <summary>
        /// Gets whether this tag is valid (not null or empty)
        /// </summary>
        public bool IsValid => !string.IsNullOrEmpty(tagName);

        /// <summary>
        /// Private constructor - use Create method instead
        /// </summary>
        private GameplayTag(string tagName)
        {
            // Simple validation here to avoid circular dependency
            this.tagName = ValidateTagNameSimple(tagName);
            this.cachedHash = this.tagName?.GetHashCode() ?? 0;
        }

        /// <summary>
        /// Creates a GameplayTag from a string with caching
        /// </summary>
        public static GameplayTag Create(string tagName)
        {
            // Handle null/empty cases
            if (string.IsNullOrWhiteSpace(tagName))
                return None;

            var validatedTagName = ValidateTagNameSimple(tagName);
            if (string.IsNullOrEmpty(validatedTagName))
                return None;

            // Check cache first
            if (TagCache.TryGetValue(validatedTagName, out var cachedTag))
                return cachedTag;

            // Create new tag and cache it
            var newTag = new GameplayTag(validatedTagName);
            TagCache[validatedTagName] = newTag;
            return newTag;
        }

        /// <summary>
        /// Simple tag validation to avoid circular dependency with GameplayTagManager
        /// </summary>
        private static string ValidateTagNameSimple(string tagName)
        {
            if (string.IsNullOrWhiteSpace(tagName)) return null;
            
            // Remove leading/trailing whitespace and dots
            tagName = tagName.Trim().Trim('.');
            
            if (string.IsNullOrEmpty(tagName)) return null;
            
            // Replace multiple consecutive dots with single dots
            while (tagName.Contains(".."))
            {
                tagName = tagName.Replace("..", ".");
            }
            
            return tagName;
        }

        /// <summary>
        /// Checks if this tag matches another tag exactly
        /// </summary>
        public bool MatchesTagExact(GameplayTag other)
        {
            return string.Equals(tagName, other.tagName, StringComparison.Ordinal);
        }

        /// <summary>
        /// Checks if this tag matches any part of another tag's hierarchy
        /// Example: "Ability.Fire" matches "Ability.Fire.Fireball"
        /// </summary>
        public bool MatchesTag(GameplayTag other)
        {
            if (!IsValid || !other.IsValid) return false;
            
            // Exact match
            if (MatchesTagExact(other)) return true;
            
            // Check if this tag is a parent of the other tag
            return other.tagName.StartsWith(tagName + ".", StringComparison.Ordinal);
        }

        /// <summary>
        /// Gets all parent tags in the hierarchy
        /// Example: "Ability.Fire.Fireball" returns ["Ability", "Ability.Fire"]
        /// </summary>
        public IEnumerable<GameplayTag> GetParentTags()
        {
            if (!IsValid) yield break;
            
            var parts = tagName.Split('.');
            for (var i = 1; i < parts.Length; i++)
            {
                var parentTag = string.Join(".", parts, 0, i);
                yield return Create(parentTag);
            }
        }

        /// <summary>
        /// Gets the immediate parent tag
        /// Example: "Ability.Fire.Fireball" returns "Ability.Fire"
        /// </summary>
        public GameplayTag GetParentTag()
        {
            if (!IsValid) return None;
            
            var lastDotIndex = tagName.LastIndexOf('.');
            if (lastDotIndex <= 0) return None;
            
            return Create(tagName.Substring(0, lastDotIndex));
        }

        /// <summary>
        /// Gets the depth of this tag in the hierarchy
        /// Example: "Ability.Fire.Fireball" returns 3
        /// </summary>
        public int GetDepth()
        {
            if (!IsValid) return 0;
            return tagName.Split('.').Length;
        }

        public override bool Equals(object obj)
        {
            return obj is GameplayTag other && Equals(other);
        }

        public bool Equals(GameplayTag other)
        {
            return string.Equals(tagName, other.tagName, StringComparison.Ordinal);
        }

        public override int GetHashCode()
        {
            return cachedHash;
        }

        public int CompareTo(GameplayTag other)
        {
            return string.Compare(tagName, other.tagName, StringComparison.Ordinal);
        }

        public override string ToString()
        {
            return TagName;
        }

        // Implicit conversion from string
        public static implicit operator GameplayTag(string tagName)
        {
            return Create(tagName);
        }

        // Implicit conversion to string
        public static implicit operator string(GameplayTag tag)
        {
            return tag.TagName;
        }

        public static bool operator ==(GameplayTag left, GameplayTag right)
        {
            return left.Equals(right);
        }

        public static bool operator !=(GameplayTag left, GameplayTag right)
        {
            return !left.Equals(right);
        }

        /// <summary>
        /// Tries to parse a string into a GameplayTag
        /// </summary>
        /// <param name="tagString">The string to parse</param>
        /// <param name="tag">The resulting GameplayTag if parsing succeeds</param>
        /// <returns>True if parsing succeeds, false otherwise</returns>
        public static bool TryParse(string tagString, out GameplayTag tag)
        {
            if (string.IsNullOrWhiteSpace(tagString))
            {
                tag = None;
                return false;
            }

            var validatedTag = ValidateTagNameSimple(tagString);
            if (string.IsNullOrEmpty(validatedTag))
            {
                tag = None;
                return false;
            }

            tag = Create(validatedTag);
            return true;
        }
    }
}