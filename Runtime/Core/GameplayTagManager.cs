using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace GameplayTags.Core
{
    /// <summary>
    /// Singleton manager for GameplayTags registration, validation, and lookup
    /// </summary>
    public class GameplayTagManager : MonoBehaviour
    {
        private static GameplayTagManager _instance;
        public static GameplayTagManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<GameplayTagManager>();
                    if (_instance == null)
                    {
                        var go = new GameObject("GameplayTagManager");
                        _instance = go.AddComponent<GameplayTagManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        [SerializeField] private List<string> registeredTags = new();
        [SerializeField] private bool autoRegisterTags = true;
        [SerializeField] private GameplayTagSettings tagSettings;

        private HashSet<string> tagLookup = new();
        private Dictionary<string, List<string>> parentToChildren = new();

        public event Action<GameplayTag> OnTagRegistered;
        public event Action<GameplayTag> OnTagUnregistered;
        
        /// <summary>
        /// Gets or sets the tag settings asset
        /// </summary>
        public GameplayTagSettings TagSettings
        {
            get => tagSettings;
            set
            {
                if (tagSettings != value)
                {
                    tagSettings = value;
                    if (tagSettings != null && tagSettings.AutoRegisterOnAwake)
                    {
                        LoadTagsFromSettings();
                    }
                }
            }
        }

        private void Awake()
        {
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }
            
            _instance = this;
            DontDestroyOnLoad(gameObject);
            
            // Load tags from settings if available and auto-register is enabled
            if (tagSettings != null && tagSettings.AutoRegisterOnAwake)
            {
                LoadTagsFromSettings();
            }
            
            InitializeTagLookup();
        }

        private void InitializeTagLookup()
        {
            tagLookup.Clear();
            parentToChildren.Clear();
            
            foreach (var tag in registeredTags)
            {
                if (!string.IsNullOrEmpty(tag))
                {
                    RegisterTagInternal(tag);
                }
            }
        }

        /// <summary>
        /// Loads tags from the tag settings asset
        /// </summary>
        private void LoadTagsFromSettings()
        {
            if (tagSettings == null) return;
            
            foreach (var tag in tagSettings.DefaultTags)
            {
                if (!string.IsNullOrEmpty(tag) && !registeredTags.Contains(tag))
                {
                    registeredTags.Add(tag);
                }
            }
        }

        /// <summary>
        /// Registers a new gameplay tag
        /// </summary>
        public bool RegisterTag(string tagName)
        {
            var validatedName = ValidateTagName(tagName);
            if (string.IsNullOrEmpty(validatedName)) return false;
            
            if (tagLookup.Contains(validatedName)) return false;
            
            RegisterTagInternal(validatedName);
            
            if (!registeredTags.Contains(validatedName))
            {
                registeredTags.Add(validatedName);
            }
            
            OnTagRegistered?.Invoke(GameplayTag.Create(validatedName));
            return true;
        }

        /// <summary>
        /// Registers multiple gameplay tags
        /// </summary>
        public void RegisterTags(IEnumerable<string> tagNames)
        {
            foreach (var tagName in tagNames)
            {
                RegisterTag(tagName);
            }
        }

        /// <summary>
        /// Unregisters a gameplay tag
        /// </summary>
        public bool UnregisterTag(string tagName)
        {
            var validatedName = ValidateTagName(tagName);
            if (string.IsNullOrEmpty(validatedName) || !tagLookup.Contains(validatedName))
                return false;
            
            tagLookup.Remove(validatedName);
            registeredTags.Remove(validatedName);
            
            // Remove from parent-child relationships
            var parentTag = GetParentTagName(validatedName);
            if (!string.IsNullOrEmpty(parentTag) && parentToChildren.ContainsKey(parentTag))
            {
                parentToChildren[parentTag].Remove(validatedName);
                if (parentToChildren[parentTag].Count == 0)
                {
                    parentToChildren.Remove(parentTag);
                }
            }
            
            OnTagUnregistered?.Invoke(GameplayTag.Create(validatedName));
            return true;
        }

        /// <summary>
        /// Checks if a tag is registered
        /// </summary>
        public bool IsTagRegistered(string tagName)
        {
            var validatedName = ValidateTagName(tagName);
            return !string.IsNullOrEmpty(validatedName) && tagLookup.Contains(validatedName);
        }

        /// <summary>
        /// Gets all registered tags
        /// </summary>
        public IEnumerable<GameplayTag> GetAllRegisteredTags()
        {
            return registeredTags.Select(tag => GameplayTag.Create(tag));
        }

        /// <summary>
        /// Gets all child tags of a parent tag
        /// </summary>
        public IEnumerable<GameplayTag> GetChildTags(string parentTagName)
        {
            var validatedName = ValidateTagName(parentTagName);
            if (string.IsNullOrEmpty(validatedName) || !parentToChildren.ContainsKey(validatedName))
                return Enumerable.Empty<GameplayTag>();
            
            return parentToChildren[validatedName].Select(tag => GameplayTag.Create(tag));
        }

        /// <summary>
        /// Gets all tags that start with a given prefix
        /// </summary>
        public IEnumerable<GameplayTag> GetTagsWithPrefix(string prefix)
        {
            var validatedPrefix = ValidateTagName(prefix);
            if (string.IsNullOrEmpty(validatedPrefix)) return Enumerable.Empty<GameplayTag>();
            
            return registeredTags
                .Where(tag => tag.StartsWith(validatedPrefix + ".", StringComparison.Ordinal) || tag == validatedPrefix)
                .Select(tag => GameplayTag.Create(tag));
        }

        /// <summary>
        /// Validates and normalizes a tag name
        /// </summary>
        public static string ValidateTagName(string tagName)
        {
            if (string.IsNullOrWhiteSpace(tagName)) return null;
            
            // Remove leading/trailing whitespace and dots
            tagName = tagName.Trim().Trim('.');
            
            if (string.IsNullOrEmpty(tagName)) return null;
            
            // Replace multiple consecutive dots with single dots
            while (tagName.Contains(".."))
            {
                tagName = tagName.Replace("..", ".");
            }
            
            // Validate characters - only allow alphanumeric, dots, and underscores
            for (var i = 0; i < tagName.Length; i++)
            {
                var c = tagName[i];
                if (!char.IsLetterOrDigit(c) && c != '.' && c != '_')
                {
                    Debug.LogWarning($"Invalid character '{c}' in tag name: {tagName}");
                    return null;
                }
            }
            
            return tagName;
        }

        /// <summary>
        /// Creates a GameplayTag and optionally registers it
        /// </summary>
        public GameplayTag CreateTag(string tagName, bool autoRegister = true)
        {
            var validatedName = ValidateTagName(tagName);
            if (string.IsNullOrEmpty(validatedName)) return GameplayTag.None;
            
            if (autoRegister && autoRegisterTags)
            {
                RegisterTag(validatedName);
            }
            
            return GameplayTag.Create(validatedName);
        }

        private void RegisterTagInternal(string tagName)
        {
            tagLookup.Add(tagName);
            
            // Build parent-child relationships
            var parentTag = GetParentTagName(tagName);
            if (!string.IsNullOrEmpty(parentTag))
            {
                if (!parentToChildren.ContainsKey(parentTag))
                {
                    parentToChildren[parentTag] = new List<string>();
                }
                
                if (!parentToChildren[parentTag].Contains(tagName))
                {
                    parentToChildren[parentTag].Add(tagName);
                }
                
                // Recursively register parent tags
                if (!tagLookup.Contains(parentTag))
                {
                    RegisterTagInternal(parentTag);
                    if (!registeredTags.Contains(parentTag))
                    {
                        registeredTags.Add(parentTag);
                    }
                }
            }
        }

        private string GetParentTagName(string tagName)
        {
            var lastDotIndex = tagName.LastIndexOf('.');
            return lastDotIndex > 0 ? tagName.Substring(0, lastDotIndex) : null;
        }

        [ContextMenu("Validate All Tags")]
        public void ValidateAllTags()
        {
            var invalidTags = new List<string>();
            
            foreach (var tag in registeredTags.ToList())
            {
                if (ValidateTagName(tag) != tag)
                {
                    invalidTags.Add(tag);
                }
            }
            
            foreach (var invalidTag in invalidTags)
            {
                Debug.LogWarning($"Removing invalid tag: {invalidTag}");
                registeredTags.Remove(invalidTag);
            }
            
            InitializeTagLookup();
        }

        [ContextMenu("Clear All Tags")]
        public void ClearAllTags()
        {
            registeredTags.Clear();
            tagLookup.Clear();
            parentToChildren.Clear();
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            if (Application.isPlaying)
            {
                InitializeTagLookup();
            }
        }
#endif
    }
}
