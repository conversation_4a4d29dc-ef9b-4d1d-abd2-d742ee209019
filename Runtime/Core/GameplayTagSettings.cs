using System.Collections.Generic;
using UnityEngine;

namespace GameplayTags.Core
{
    /// <summary>
    /// ScriptableObject that stores default gameplay tags configuration
    /// </summary>
    [CreateAssetMenu(fileName = "GameplayTagSettings", menuName = "Gameplay Tags/Tag Settings", order = 1)]
    public class GameplayTagSettings : ScriptableObject
    {
        [Header("Default Tags")]
        [SerializeField] private List<string> defaultTags = new();
        
        [Header("Settings")]
        [SerializeField] private bool autoRegisterOnAwake = true;
        [SerializeField] private bool validateTagsOnBuild = true;
        [SerializeField] private bool allowDynamicTagRegistration = true;
        
        /// <summary>
        /// Gets the list of default tags
        /// </summary>
        public List<string> DefaultTags => defaultTags;
        
        /// <summary>
        /// Gets whether tags should be auto-registered on awake
        /// </summary>
        public bool AutoRegisterOnAwake => autoRegisterOnAwake;
        
        /// <summary>
        /// Gets whether tags should be validated on build
        /// </summary>
        public bool ValidateTagsOnBuild => validateTagsOnBuild;
        
        /// <summary>
        /// Gets whether dynamic tag registration is allowed
        /// </summary>
        public bool AllowDynamicTagRegistration => allowDynamicTagRegistration;
        
        /// <summary>
        /// Adds a tag to the default tags list if it doesn't already exist
        /// </summary>
        public bool AddDefaultTag(string tagName)
        {
            if (string.IsNullOrEmpty(tagName) || defaultTags.Contains(tagName))
                return false;
                
            defaultTags.Add(tagName);
            return true;
        }
        
        /// <summary>
        /// Removes a tag from the default tags list
        /// </summary>
        public bool RemoveDefaultTag(string tagName)
        {
            return defaultTags.Remove(tagName);
        }
        
        /// <summary>
        /// Clears all default tags
        /// </summary>
        public void ClearDefaultTags()
        {
            defaultTags.Clear();
        }
        
        /// <summary>
        /// Gets all parent tags for hierarchical display
        /// </summary>
        public HashSet<string> GetParentTags()
        {
            var parentTags = new HashSet<string>();
            
            foreach (var tag in defaultTags)
            {
                var parts = tag.Split('.');
                for (var i = 0; i < parts.Length - 1; i++)
                {
                    var parentTag = string.Join(".", parts, 0, i + 1);
                    parentTags.Add(parentTag);
                }
            }
            
            return parentTags;
        }
        
        /// <summary>
        /// Gets child tags of a parent tag
        /// </summary>
        public List<string> GetChildTags(string parentTag)
        {
            var childTags = new List<string>();
            var parentDotCount = CountCharacter(parentTag, '.');
            
            foreach (var tag in defaultTags)
            {
                if (tag.StartsWith(parentTag + ".") && CountCharacter(tag, '.') == parentDotCount + 1)
                {
                    childTags.Add(tag);
                }
            }
            
            return childTags;
        }
        
        /// <summary>
        /// Helper method to count occurrences of a character in a string
        /// </summary>
        private int CountCharacter(string text, char character)
        {
            var count = 0;
            foreach (var c in text)
            {
                if (c == character)
                    count++;
            }
            return count;
        }
        
        /// <summary>
        /// Validates and sorts the default tags
        /// </summary>
        [ContextMenu("Validate and Sort Tags")]
        public void ValidateAndSortTags()
        {
            // Remove empty or invalid tags
            defaultTags.RemoveAll(tag => string.IsNullOrWhiteSpace(tag));
            
            // Remove duplicates
            var uniqueTags = new HashSet<string>(defaultTags);
            defaultTags.Clear();
            defaultTags.AddRange(uniqueTags);
            
            // Sort alphabetically
            defaultTags.Sort();
            
            #if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
            #endif
        }
    }
}
