using System;
using UnityEngine;
using PurrNet.Packing;

namespace GameplayTags.Core
{
    /// <summary>
    /// Represents a gameplay tag with its source, allowing multiple instances of the same tag
    /// from different sources to coexist in a container
    /// </summary>
    [Serializable]
    public struct TagStack : IEquatable<TagStack>, IComparable<TagStack>, IPackedAuto
    {
        [SerializeField] private GameplayTag tag;
        [SerializeField] private TagSource source;

        /// <summary>
        /// Gets the gameplay tag
        /// </summary>
        public GameplayTag Tag => tag;

        /// <summary>
        /// Gets the source of this tag
        /// </summary>
        public TagSource Source => source;

        /// <summary>
        /// Gets whether this tag stack is valid (both tag and source are valid)
        /// </summary>
        public bool IsValid => tag.IsValid && source.IsValid;

        /// <summary>
        /// Creates a new TagStack with the specified tag and source
        /// </summary>
        public TagStack(GameplayTag tag, TagSource source)
        {
            this.tag = tag;
            this.source = source;
        }

        /// <summary>
        /// Creates a new TagStack with the specified tag and source name
        /// </summary>
        public TagStack(GameplayTag tag, string sourceName)
        {
            this.tag = tag;
            this.source = new TagSource(sourceName);
        }

        /// <summary>
        /// Creates a new TagStack with the specified tag name and source name
        /// </summary>
        public TagStack(string tagName, string sourceName)
        {
            this.tag = GameplayTag.Create(tagName);
            this.source = new TagSource(sourceName);
        }

        /// <summary>
        /// Checks if this tag stack matches another tag (ignores source)
        /// </summary>
        public bool MatchesTag(GameplayTag otherTag)
        {
            return tag.MatchesTag(otherTag);
        }

        /// <summary>
        /// Checks if this tag stack has the same tag as another stack (ignores source)
        /// </summary>
        public bool HasSameTag(TagStack other)
        {
            return tag.Equals(other.tag);
        }

        /// <summary>
        /// Checks if this tag stack has the same source as another stack (ignores tag)
        /// </summary>
        public bool HasSameSource(TagStack other)
        {
            return source.Equals(other.source);
        }

        public bool Equals(TagStack other)
        {
            return tag.Equals(other.tag) && source.Equals(other.source);
        }

        public override bool Equals(object obj)
        {
            return obj is TagStack other && Equals(other);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return (tag.GetHashCode() * 397) ^ source.GetHashCode();
            }
        }

        public int CompareTo(TagStack other)
        {
            var tagComparison = tag.CompareTo(other.tag);
            return tagComparison != 0 ? tagComparison : source.CompareTo(other.source);
        }

        public override string ToString()
        {
            return $"{tag} [{source}]";
        }

        // Operators
        public static bool operator ==(TagStack left, TagStack right)
        {
            return left.Equals(right);
        }

        public static bool operator !=(TagStack left, TagStack right)
        {
            return !left.Equals(right);
        }

        // Serialization
        public void Serialize(BitPacker packer)
        {
            Packer<GameplayTag>.Write(packer, tag);
            Packer<TagSource>.Write(packer, source);
        }

        public void Deserialize(BitPacker packer)
        {
            Packer<GameplayTag>.Read(packer, ref tag);
            Packer<TagSource>.Read(packer, ref source);
        }
    }
}
