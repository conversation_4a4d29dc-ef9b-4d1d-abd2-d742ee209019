using System;
using UnityEngine;
using PurrNet.Packing;

namespace GameplayTags.Core
{
    /// <summary>
    /// Represents a source for gameplay tags, typically identifying where a tag comes from
    /// (e.g., equipment slot, ability, passive effect, etc.)
    /// </summary>
    [Serializable]
    public struct TagSource : IEquatable<TagSource>, IComparable<TagSource>, IPackedAuto
    {
        [SerializeField] private string sourceName;
        [SerializeField] private int sourceHash;

        public static readonly TagSource None = new();

        /// <summary>
        /// Gets the source name (e.g., "Equipment_Slot_Head", "Ability_Fireball")
        /// </summary>
        public string SourceName => sourceName ?? string.Empty;

        /// <summary>
        /// Gets the cached hash of the source name for efficient lookups
        /// </summary>
        public int SourceHash => sourceHash;

        /// <summary>
        /// Gets whether this source is valid (not null or empty)
        /// </summary>
        public bool IsValid => !string.IsNullOrEmpty(sourceName);

        /// <summary>
        /// Creates a new TagSource from a string
        /// </summary>
        public TagSource(string sourceName)
        {
            this.sourceName = string.IsNullOrWhiteSpace(sourceName) ? null : sourceName.Trim();
            this.sourceHash = this.sourceName?.GetHashCode() ?? 0;
        }

        public bool Equals(TagSource other)
        {
            return sourceHash == other.sourceHash && 
                   string.Equals(sourceName, other.sourceName, StringComparison.Ordinal);
        }

        public override bool Equals(object obj)
        {
            return obj is TagSource other && Equals(other);
        }

        public override int GetHashCode()
        {
            return sourceHash;
        }

        public int CompareTo(TagSource other)
        {
            return string.Compare(sourceName, other.sourceName, StringComparison.Ordinal);
        }

        public override string ToString()
        {
            return IsValid ? sourceName : "None";
        }

        // Operators
        public static bool operator ==(TagSource left, TagSource right)
        {
            return left.Equals(right);
        }

        public static bool operator !=(TagSource left, TagSource right)
        {
            return !left.Equals(right);
        }

        public static implicit operator TagSource(string sourceName)
        {
            return new TagSource(sourceName);
        }

        public static implicit operator string(TagSource source)
        {
            return source.SourceName;
        }

        // Serialization
        public void Serialize(BitPacker packer)
        {
            Packer<string>.Write(packer, sourceName);
            Packer<int>.Write(packer, sourceHash);
        }

        public void Deserialize(BitPacker packer)
        {
            Packer<string>.Read(packer, ref sourceName);
            Packer<int>.Read(packer, ref sourceHash);
        }
    }
}
