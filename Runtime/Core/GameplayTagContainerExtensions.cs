using System.Collections.Generic;
using System.Linq;

namespace GameplayTags.Core
{
    /// <summary>
    /// Extension methods for GameplayTagContainer to provide easier access patterns
    /// </summary>
    public static class GameplayTagContainerExtensions
    {
        /// <summary>
        /// Adds a tag with a string source name
        /// </summary>
        public static bool AddTag(this GameplayTagContainer container, string tagName, string sourceName)
        {
            return container.AddTag(GameplayTag.Create(tagName), new TagSource(sourceName));
        }

        /// <summary>
        /// Checks if the container has a tag from any source
        /// </summary>
        public static bool HasTag(this GameplayTagContainer container, string tagName)
        {
            return container.HasTag(GameplayTag.Create(tagName));
        }

        /// <summary>
        /// Gets the count of a specific tag across all sources
        /// </summary>
        public static int GetTagStackCount(this GameplayTagContainer container, string tagName)
        {
            return container.GetTagStackCount(GameplayTag.Create(tagName));
        }

        /// <summary>
        /// Removes all instances of a tag by name
        /// </summary>
        public static bool RemoveAllInstancesOfTag(this GameplayTagContainer container, string tagName)
        {
            return container.RemoveAllInstancesOfTag(GameplayTag.Create(tagName));
        }

        /// <summary>
        /// Gets all tag names (unique) as strings
        /// </summary>
        public static IEnumerable<string> GetTagNames(this GameplayTagContainer container)
        {
            return container.GetUniqueTags().Select(tag => tag.TagName);
        }

        /// <summary>
        /// Gets all source names in the container
        /// </summary>
        public static IEnumerable<string> GetSourceNames(this GameplayTagContainer container)
        {
            return container.Select(stack => stack.Source.SourceName).Distinct();
        }

        /// <summary>
        /// Creates a container from tag names with a common source
        /// </summary>
        public static GameplayTagContainer FromTagNames(IEnumerable<string> tagNames, string sourceName)
        {
            var container = new GameplayTagContainer();
            var source = new TagSource(sourceName);
            
            foreach (var tagName in tagNames)
            {
                container.AddTag(GameplayTag.Create(tagName), source);
            }
            
            return container;
        }

        /// <summary>
        /// Merges another container into this one, preserving all stacks
        /// </summary>
        public static void Merge(this GameplayTagContainer container, GameplayTagContainer other)
        {
            container.AddTagStacks(other);
        }

        /// <summary>
        /// Gets a summary of the container for debugging
        /// </summary>
        public static string GetDebugSummary(this GameplayTagContainer container)
        {
            var uniqueTags = container.GetUniqueTags().ToList();
            var sources = container.GetSourceNames().ToList();
            
            return $"Container: {container.Count} stacks, {uniqueTags.Count} unique tags, {sources.Count} sources\n" +
                   $"Tags: [{string.Join(", ", uniqueTags.Select(t => t.TagName))}]\n" +
                   $"Sources: [{string.Join(", ", sources)}]";
        }
    }
}
