using UnityEngine;
using GameplayTags.Core;

namespace GameplayTags.Examples
{
    /// <summary>
    /// Example demonstrating the new tag stacking system with sources
    /// </summary>
    public class TagStackingExample : MonoBehaviour
    {
        [Header("Tag Container")]
        public GameplayTagContainer tagContainer = new();

        private void Start()
        {
            DemonstrateTagStacking();
        }

        private void DemonstrateTagStacking()
        {
            Debug.Log("=== GameplayTag Stacking Example ===");

            // Clear any existing tags
            tagContainer.Clear();

            // Add the same tag from different sources
            tagContainer.AddTag("Status.Buff.Strength", "Equipment_Slot_Head");
            tagContainer.AddTag("Status.Buff.Strength", "Equipment_Slot_Chest");
            tagContainer.AddTag("Status.Buff.Strength", "Ability_PowerUp");
            tagContainer.AddTag("Status.Buff.Speed", "Equipment_Slot_Boots");
            tagContainer.AddTag("Status.Debuff.Weakness", "Environment_Poison");

            // Display container summary
            Debug.Log($"Container Summary: {tagContainer}");
            Debug.Log($"Debug Summary:\n{tagContainer.GetDebugSummary()}");

            // Check tag stacking
            var strengthTag = GameplayTag.Create("Status.Buff.Strength");
            Debug.Log($"Strength tag count: {tagContainer.GetTagStackCount(strengthTag)}");
            Debug.Log($"Sources providing Strength: {string.Join(", ", tagContainer.GetSourcesForTag(strengthTag))}");

            // Remove from specific source
            Debug.Log("\nRemoving Strength from Equipment_Slot_Head...");
            tagContainer.RemoveTag(strengthTag, "Equipment_Slot_Head");
            Debug.Log($"Strength tag count after removal: {tagContainer.GetTagStackCount(strengthTag)}");

            // Check hierarchical matching
            var buffTag = GameplayTag.Create("Status.Buff");
            Debug.Log($"\nTags matching 'Status.Buff': {string.Join(", ", tagContainer.GetTagsMatching(buffTag))}");

            // Remove all tags from a source
            Debug.Log("\nRemoving all tags from Equipment_Slot_Chest...");
            var removedCount = tagContainer.RemoveTagsFromSource("Equipment_Slot_Chest");
            Debug.Log($"Removed {removedCount} tag stacks");

            // Final state
            Debug.Log($"\nFinal container: {tagContainer}");
        }

        // Example methods that could be called from Unity Inspector or other scripts
        [ContextMenu("Add Sample Tags")]
        public void AddSampleTags()
        {
            tagContainer.AddTag("Combat.Weapon.Sword", "Equipment_MainHand");
            tagContainer.AddTag("Combat.Armor.Heavy", "Equipment_Chest");
            tagContainer.AddTag("Magic.Element.Fire", "Spell_Fireball");
        }

        [ContextMenu("Clear All Tags")]
        public void ClearAllTags()
        {
            tagContainer.Clear();
            Debug.Log("All tags cleared!");
        }

        [ContextMenu("Print Tag Summary")]
        public void PrintTagSummary()
        {
            Debug.Log($"Tag Summary:\n{tagContainer.GetDebugSummary()}");
        }
    }
}
