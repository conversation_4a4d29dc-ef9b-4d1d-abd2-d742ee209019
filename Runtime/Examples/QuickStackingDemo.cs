using UnityEngine;
using GameplayTags.Core;

namespace GameplayTags.Examples
{
    /// <summary>
    /// Quick demo showing the key features of the tag stacking system
    /// </summary>
    public class QuickStackingDemo : MonoBehaviour
    {
        private void Start()
        {
            var container = new GameplayTagContainer();
            
            Debug.Log("=== Tag Stacking Demo ===");
            
            // 1. Add same tag from different sources
            container.AddTag("Buff.Strength", "Equipment_Head");
            container.AddTag("Buff.Strength", "Equipment_Gloves");  
            container.AddTag("Buff.Strength", "Potion_Strength");
            container.AddTag("Buff.Speed", "Equipment_Boots");
            
            Debug.Log($"Total stacks: {container.Count}");
            Debug.Log($"Unique tags: {container.UniqueTagCount}");
            Debug.Log($"Strength stacks: {container.GetTagStackCount("Buff.Strength")}");
            
            // 2. List all sources for Strength
            var strengthSources = container.GetSourcesForTag("Buff.Strength");
            Debug.Log($"Strength sources: {string.Join(", ", strengthSources)}");
            
            // 3. Remove from specific source
            container.RemoveTag("Buff.Strength", "Equipment_Head");
            Debug.Log($"After removing head equipment - Strength stacks: {container.GetTagStackCount("Buff.Strength")}");
            
            // 4. Remove all equipment (multiple tags)
            container.RemoveTagsFromSource("Equipment_Gloves");
            container.RemoveTagsFromSource("Equipment_Boots");
            Debug.Log($"After removing equipment - Total stacks: {container.Count}");
            
            // 5. Check if still has strength (from potion)
            Debug.Log($"Still has strength buff: {container.HasTag("Buff.Strength")}");
            
            // 6. Show iteration over TagStacks (the main API)
            Debug.Log("\n=== All Current Tag Stacks ===");
            foreach (var tagStack in container)
            {
                Debug.Log($"Tag: {tagStack.Tag.TagName} from Source: {tagStack.Source.SourceName}");
            }
        }
    }
}
