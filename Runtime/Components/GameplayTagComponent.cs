using UnityEngine;
using PurrNet;
using GameplayTags.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GameplayTags.Components
{
    /// <summary>
    /// GameplayTag component with source tracking and networking support
    /// </summary>
    public class GameplayTagComponent : NetworkIdentity
    {
        [Header("Gameplay Tags")]
        [Tooltip("Tags applied when the object spawns.")]
        [SerializeField] private GameplayTagContainer initialTags = new();
        
        [Tooltip("Log tag changes for debugging.")]
        [SerializeField] private bool logTagChanges;
        
        [Header("Network Settings")]
        [Tooltip("Enable network synchronization.")]
        [SerializeField] private bool useNetworking = true;

        [Tooltip("Allow object owner to modify tags in multiplayer.")]
        [SerializeField] private bool allowOwnerModification;

        // Network/Local storage
        [SerializeField, HideInInspector] private SyncVar<GameplayTagContainer> networkTags = new();
        private GameplayTagContainer localTags = new();

        // Properties
        public GameplayTagContainer GameplayTags => useNetworking ? (networkTags.value ?? new GameplayTagContainer()) : localTags;
        public bool HasTags => !GameplayTags.IsEmpty;
        public int TagCount => GameplayTags.Count;

        // Events
        public Action<GameplayTag> OnTagAdded;
        public Action<GameplayTag> OnTagRemoved;  
        public Action OnTagsChanged;

        protected override void OnSpawned()
        {
            base.OnSpawned();
            
            if (useNetworking)
            {
                if (isServer && networkTags.value == null)
                    networkTags.value = new GameplayTagContainer();
                
                networkTags.onChanged += OnTagsChangedInternal;
                
                if (isServer)
                    InitializeTags(networkTags.value);
            }
        }

        protected override void OnDespawned()
        {
            if (useNetworking)
                networkTags.onChanged -= OnTagsChangedInternal;
            base.OnDespawned();
        }

        void Start()
        {
            if (!useNetworking)
                InitializeTags(localTags);
        }

        private void InitializeTags(GameplayTagContainer container)
        {
            if (initialTags.Count > 0)
            {
                foreach (var tagStack in initialTags)
                    container.AddTagStack(tagStack);
            }
        }

        private void OnTagsChangedInternal(GameplayTagContainer newContainer)
        {
            if (logTagChanges)
                Debug.Log($"[{gameObject.name}] Tags changed. Count: {newContainer?.Count ?? 0}");
            OnTagsChanged?.Invoke();
        }

        #region Public API

        /// <summary>
        /// Add a tag with a specific source
        /// </summary>
        public void AddTag(GameplayTag tag, string source = "Component")
        {
            ExecuteTagOperation(() => GetContainer().AddTag(tag, source), 
                              () => OnTagAdded?.Invoke(tag),
                              $"Added tag: {tag} from {source}");
        }

        /// <summary>
        /// Add a tag stack directly
        /// </summary>
        public void AddTagStack(TagStack tagStack)
        {
            ExecuteTagOperation(() => GetContainer().AddTagStack(tagStack),
                              () => OnTagAdded?.Invoke(tagStack.Tag),
                              $"Added tag stack: {tagStack}");
        }

        /// <summary>
        /// Remove all instances of a tag
        /// </summary>
        public void RemoveTag(GameplayTag tag)
        {
            ExecuteTagOperation(() => GetContainer().RemoveAllInstancesOfTag(tag),
                              () => OnTagRemoved?.Invoke(tag),
                              $"Removed tag: {tag}");
        }

        /// <summary>
        /// Remove tag from specific source
        /// </summary>
        public void RemoveTagFromSource(GameplayTag tag, string source)
        {
            ExecuteTagOperation(() => GetContainer().RemoveTag(tag, source),
                              () => OnTagRemoved?.Invoke(tag),
                              $"Removed tag: {tag} from {source}");
        }

        /// <summary>
        /// Remove all tags from a source
        /// </summary>
        public void RemoveTagsFromSource(string source)
        {
            ExecuteTagOperation(() => GetContainer().RemoveTagsFromSource(source) > 0,
                              () => OnTagsChanged?.Invoke(),
                              $"Removed tags from source: {source}");
        }

        /// <summary>
        /// Clear all tags
        /// </summary>
        public void ClearTags()
        {
            ExecuteTagOperation(() => { GetContainer().Clear(); return true; },
                              () => OnTagsChanged?.Invoke(),
                              "Cleared all tags");
        }

        /// <summary>
        /// Add multiple tag stacks
        /// </summary>
        public void AddTagStacks(IEnumerable<TagStack> tagStacks)
        {
            foreach (var stack in tagStacks)
                AddTagStack(stack);
        }

        #endregion

        #region Core Implementation

        private GameplayTagContainer GetContainer()
        {
            return useNetworking ? (networkTags.value ?? new GameplayTagContainer()) : localTags;
        }

        private void ExecuteTagOperation(Func<bool> operation, Action onSuccess, string logMessage)
        {
            if (!CanModifyTags())
            {
                Debug.LogWarning($"Cannot modify tags - insufficient permissions");
                return;
            }

            bool success;
            if (useNetworking && !isServer)
            {
                // For networked clients, we'll implement RPC calls here
                RequestTagOperation(operation, onSuccess, logMessage);
                return;
            }

            // Direct execution for server or local mode
            success = operation();
            
            if (success)
            {
                onSuccess?.Invoke();
                if (logTagChanges)
                    Debug.Log($"[{gameObject.name}] {logMessage}");
                
                if (useNetworking && isServer)
                    networkTags.value = GetContainer(); // Trigger network sync
            }
        }

        private bool CanModifyTags()
        {
            if (!useNetworking) return true;
            return isServer || (allowOwnerModification && isOwner);
        }

        private void RequestTagOperation(Func<bool> operation, Action onSuccess, string logMessage)
        {
            // For now, just log that we need RPC implementation
            Debug.LogWarning("RPC tag operations not yet implemented in cleaned version");
        }

        #endregion

        #region Query API

        public bool HasTag(GameplayTag tag) => GameplayTags.HasTag(tag);
        public bool HasTagExact(GameplayTag tag) => GameplayTags.HasTagExact(tag);
        public bool HasTagFromSource(GameplayTag tag, string source) => GameplayTags.HasTagFromSource(tag, source);
        public bool HasAllTags(GameplayTagContainer other) => GameplayTags.HasAllTags(other);
        public bool HasAnyTags(GameplayTagContainer other) => GameplayTags.HasAnyTags(other);
        public int GetTagStackCount(GameplayTag tag) => GameplayTags.GetTagStackCount(tag);
        public IEnumerable<string> GetSourcesForTag(GameplayTag tag) => GameplayTags.GetSourcesForTag(tag).Select(s => s.SourceName);

        #endregion

        #region Local Tag Operations (Non-Networked Mode)

        /// <summary>
        /// Local-only: Add a tag directly without networking
        /// </summary>
        void AddTagLocal(GameplayTag tag)
        {
            if (!tag.IsValid) return;

            if (localTags.AddTag(tag, "Component"))
            {
                OnTagAdded?.Invoke(tag);
                
                if (logTagChanges)
                    Debug.Log($"[{gameObject.name}] Added tag locally: {tag}");
                
                OnTagsChanged?.Invoke();
            }
        }

        /// <summary>
        /// Local-only: Remove a tag directly without networking
        /// </summary>
        void RemoveTagLocal(GameplayTag tag)
        {
            if (localTags.RemoveAllInstancesOfTag(tag))
            {
                OnTagRemoved?.Invoke(tag);
                
                if (logTagChanges)
                    Debug.Log($"[{gameObject.name}] Removed tag locally: {tag}");
                
                OnTagsChanged?.Invoke();
            }
        }

        /// <summary>
        /// Local-only: Clear all tags without networking
        /// </summary>
        void ClearTagsLocal()
        {
            localTags.Clear();
            
            if (logTagChanges)
                Debug.Log($"[{gameObject.name}] Cleared all tags locally");
            
            OnTagsChanged?.Invoke();
        }

        #endregion

        #region Server-Only Direct Operations

        /// <summary>
        /// Server-only: Directly add a tag without validation (server authority)
        /// </summary>
        public bool AddTagDirect(GameplayTag tag)
        {
            if (!isServer)
            {
                Debug.LogError("AddTagDirect can only be called on server!");
                return false;
            }

            if (!tag.IsValid) return false;

            var currentTags = networkTags.value ?? new GameplayTagContainer();
            if (currentTags.AddTag(tag, "Component"))
            {
                networkTags.value = currentTags; // Triggers network sync
                OnTagAdded?.Invoke(tag);
                
                if (logTagChanges)
                    Debug.Log($"[{gameObject.name}] Server added tag: {tag}");
                return true;
            }
            return false;
        }

        /// <summary>
        /// Server-only: Directly remove a tag without validation (server authority)
        /// </summary>
        public bool RemoveTagDirect(GameplayTag tag)
        {
            if (!isServer)
            {
                Debug.LogError("RemoveTagDirect can only be called on server!");
                return false;
            }

            var currentTags = networkTags.value ?? new GameplayTagContainer();
            if (currentTags.RemoveAllInstancesOfTag(tag))
            {
                networkTags.value = currentTags; // Triggers network sync
                OnTagRemoved?.Invoke(tag);
                
                if (logTagChanges)
                    Debug.Log($"[{gameObject.name}] Server removed tag: {tag}");
                return true;
            }
            return false;
        }

        /// <summary>
        /// Server-only: Clear all tags
        /// </summary>
        public void ClearTagsDirect()
        {
            if (!isServer)
            {
                Debug.LogError("ClearTagsDirect can only be called on server!");
                return;
            }

            networkTags.value = new GameplayTagContainer();
            
            if (logTagChanges)
                Debug.Log($"[{gameObject.name}] Server cleared all tags");
        }

        #endregion

        #region Client Request Methods (PurrNet Pattern)

        /// <summary>
        /// Client requests to add a tag - server validates and applies
        /// </summary>
        [ServerRpc]
        void RequestAddTag(GameplayTag tag, PlayerID requester)
        {
            if (!ValidateTagRequest(requester, tag))
                return;

            AddTagDirect(tag);
        }

        /// <summary>
        /// Client requests to remove a tag - server validates and applies
        /// </summary>
        [ServerRpc]
        void RequestRemoveTag(GameplayTag tag, PlayerID requester)
        {
            if (!ValidateTagRequest(requester, tag))
                return;

            RemoveTagDirect(tag);
        }

        /// <summary>
        /// Client requests to add multiple tags - server validates and applies
        /// </summary>
        [ServerRpc]
        void RequestAddTags(GameplayTagContainer tagsToAdd, PlayerID requester)
        {
            if (!ValidateBulkTagRequest(requester, tagsToAdd))
                return;

            foreach (var tagStack in tagsToAdd)
            {
                AddTagDirect(tagStack.Tag);
            }
        }

        /// <summary>
        /// Client requests to remove multiple tags - server validates and applies
        /// </summary>
        [ServerRpc]
        void RequestRemoveTags(GameplayTagContainer tagsToRemove, PlayerID requester)
        {
            if (!ValidateBulkTagRequest(requester, tagsToRemove))
                return;

            foreach (var tagStack in tagsToRemove)
            {
                RemoveTagDirect(tagStack.Tag);
            }
        }

        /// <summary>
        /// Client requests to clear all tags - server validates and applies
        /// </summary>
        [ServerRpc]
        void RequestClearTags(PlayerID requester)
        {
            if (!ValidateOwnershipRequest(requester))
                return;

            ClearTagsDirect();
        }

        #endregion

        #region Server Validation (Anti-Cheat)

        bool ValidateTagRequest(PlayerID requester, GameplayTag tag)
        {
            if (!ValidateOwnershipRequest(requester))
                return false;

            if (!tag.IsValid)
            {
                Debug.LogWarning($"Invalid tag '{tag}' requested by {requester}");
                return false;
            }

            return true;
        }

        bool ValidateBulkTagRequest(PlayerID requester, GameplayTagContainer requestedTags)
        {
            if (!ValidateOwnershipRequest(requester))
                return false;

            if (requestedTags == null || requestedTags.IsEmpty)
            {
                Debug.LogWarning($"Empty tag container requested by {requester}");
                return false;
            }

            // Prevent spam - limit bulk operations
            if (requestedTags.Count > 10) // Reasonable limit
            {
                Debug.LogWarning($"Too many tags ({requestedTags.Count}) requested by {requester}");
                return false;
            }

            return true;
        }

        bool ValidateOwnershipRequest(PlayerID requester)
        {
            // Only validate in networked mode
            if (!useNetworking) return true;
            
            // Only allow owner modifications if enabled
            if (!allowOwnerModification)
            {
                Debug.LogWarning($"Owner modifications disabled for {requester}");
                return false;
            }

            // Ensure the requester is the owner of this object
            if (!isOwner || NetworkManager.main.localPlayer != requester)
            {
                Debug.LogWarning($"Unauthorized request from {requester} for object owned by different player");
                return false;
            }

            return true;
        }

        #endregion

        #region Server RPCs for External Systems

        /// <summary>
        /// Server RPC for external systems to add tags with validation
        /// </summary>
        [ServerRpc(requireOwnership: false)]
        public void ServerAddTag(string tagName, PlayerID requester)
        {
            if (!useNetworking || !NetworkManager.main.playerModule.IsValidPlayer(requester)) return;
            
            var tag = GameplayTag.Create(tagName);
            if (tag.IsValid)
            {
                AddTagDirect(tag);
            }
        }

        /// <summary>
        /// Server RPC for external systems to remove tags with validation
        /// </summary>
        [ServerRpc(requireOwnership: false)]
        public void ServerRemoveTag(string tagName, PlayerID requester)
        {
            if (!useNetworking || !NetworkManager.main.playerModule.IsValidPlayer(requester)) return;
            
            var tag = GameplayTag.Create(tagName);
            if (tag.IsValid)
            {
                RemoveTagDirect(tag);
            }
        }

        #endregion

#if UNITY_EDITOR
        [ContextMenu("Print Current Tags")]
        public void PrintCurrentTags()
        {
            if (GameplayTags.IsEmpty)
                Debug.Log($"[{gameObject.name}] No tags");
            else
                Debug.Log($"[{gameObject.name}] Current tags: {string.Join(", ", GameplayTags)}");
        }

        [ContextMenu("Add Test Tags")]
        public void AddTestTags()
        {
            AddTag("Test.Debug");
            AddTag("Status.Buff.Speed");
            AddTag("Ability.Fire.Fireball");
        }
#endif
    }
}
