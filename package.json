{"name": "com.bastiansteinicke.gameplaytags", "version": "1.0.0", "displayName": "Gameplay Tags", "description": "A flexible tagging system for Unity with network synchronization support using PurrNet.", "unity": "2022.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {"dev.purrnet.purrnet": "1.13.0-beta.17"}, "keywords": ["tags", "gameplay", "networking", "purrnet", "multiplayer"], "samples": [{"displayName": "Basic Examples", "description": "Basic usage examples for the Gameplay Tags system", "path": "Samples~/BasicExamples"}]}