// ReSharper disable VirtualMemberNeverOverridden.Global
// ReSharper disable UnusedMember.Global

// ReSharper disable UnusedMemberHierarchy.Global
// ReSharper disable MemberCanBeProtected.Global
// ReSharper disable UnusedAutoPropertyAccessor.Global
// ReSharper disable UnusedParameter.Global

using PurrNet.Packing;

namespace ComponentSystem
{
    public abstract class Component : IPackedSimple
    {
        public ModularEntity OwningEntity { get; private set; }
        
        public void Added(ModularEntity entity)
        {
            OwningEntity = entity;
        }

        public void Removed(ModularEntity entity)
        {
            OwningEntity = null;
        }
        
        protected virtual void OnAdded(ModularEntity entity) {}
        protected virtual void OnRemoved(ModularEntity entity) {}
        public virtual void Serialize(BitPacker packer)
        {
            // throw new System.NotImplementedException();
        }
    }
}
