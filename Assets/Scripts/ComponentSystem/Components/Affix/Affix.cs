using System.Collections.Generic;
using GameplayTags.Core;
using PurrNet.Packing;
using UnityEngine;
using UnityEngine.Serialization;

// ReSharper disable UnusedMember.Global
// ReSharper disable UnusedAutoPropertyAccessor.Global
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable CollectionNeverQueried.Global
// ReSharper disable UnusedAutoPropertyAccessor.Local
// ReSharper disable NotAccessedField.Global

namespace ComponentSystem.Components.Affix
{
    [CreateAssetMenu(fileName = "New Affix", menuName = "ModuleSystem/Affix", order = 1)]
    public class Affix : ScriptableObject, IPackedSimple
    {
        [Tooltip("The name of this affix.")]
        public string affixName = "New Affix";
        [Tooltip("The value ranges for this affix, where x is the minimum and y is the maximum.")]
        public List<Vector2> valueRanges = new();
        [Tooltip("Atleast one of these tags that must be present on the entity for this affix to apply.")]
        public List<GameplayTag> entityTags = new();

        [SerializeField] private float roll;
        /// <summary> How good this affix rolled. 0 is worst, 1 is perfect. </summary>
        public float Roll
        {
            get => roll;
            private set => roll = value;
        }

        /// <summary> Cached value 1. </summary>
        public float Value1 { get; private set; }
        /// <summary> Cached value 2. </summary>
        public float Value2 { get; private set; }
        /// <summary> Generated values. </summary>
        public List<float> Values { get; } = new();

        /// <summary>
        /// Generates the affix values based on the roll.
        /// </summary>
        public void Generate()
        {
            Roll = Random.Range(0f, 1f);
            
            Values.Clear();
            Value1 = 0f; // Default if no ranges
            
            for (var i = 0; i < valueRanges.Count; i++)
            {
                var value = Mathf.Lerp(valueRanges[i].x, valueRanges[i].y, Roll);
                Values.Add(value);
                
                switch (i)
                {
                    case 0:
                        Value1 = value;
                        break;
                    case 1:
                        Value2 = value;
                        break;
                }
            }
        }

        public void Serialize(BitPacker packer)
        {
            Packer<float>.Serialize(packer, ref roll);
        }
    }
}