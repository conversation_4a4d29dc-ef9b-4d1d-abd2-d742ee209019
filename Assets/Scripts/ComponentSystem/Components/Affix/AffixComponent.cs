// ReSharper disable UnusedMember.Global
// ReSharper disable UnusedType.Global

using System.Collections.Generic;
using PurrNet.Packing;
using UnityEngine;
// ReSharper disable MemberCanBePrivate.Global

namespace ComponentSystem.Components.Affix
{
    public class AffixComponent : Component
    {
        public List<Affix> Affixes = new();

        public override void Serialize(BitPacker packer)
        {
            Packer<List<Affix>>.Serialize(packer, ref Affixes);
            Debug.Log("Serialized AffixComponent");
        }
    }
}