using PurrNet.Packing;
using UnityEngine;

// ReSharper disable UnusedMember.Global
// ReSharper disable UnusedAutoPropertyAccessor.Global
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable CollectionNeverQueried.Global
// ReSharper disable UnusedAutoPropertyAccessor.Local
// ReSharper disable NotAccessedField.Global

namespace ComponentSystem.Components.Base
{
    [CreateAssetMenu(fileName = "New BaseInfo", menuName = "ModuleSystem/BaseInfo", order = 1)]
    public class BaseInfo : ScriptableObject, IPackedSimple
    {
        [Tooltip("The name of this base.")]
        public string baseName = "New BaseInfo";

        public void Serialize(BitPacker packer)
        {
            Packer<string>.Serialize(packer, ref baseName);
        }
    }
}