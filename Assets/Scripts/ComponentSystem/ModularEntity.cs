using ComponentSystem.Components.Base;
using PurrNet;
using UnityEngine;

// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable CollectionNeverQueried.Global
// ReSharper disable UnusedMember.Global
// ReSharper disable VirtualMemberNeverOverridden.Global
// ReSharper disable MemberCanBeProtected.Global

namespace ComponentSystem
{
    public abstract class ModularEntity : NetworkBehaviour
    {
        public SyncList<Component> components = new();

        protected virtual void Start()
        {
            AddDefaultModules();
        }

        protected virtual void AddDefaultModules()
        {
            AddModule(new BaseInfoComponent());
            Debug.Log("Added default modules");
        }
        
        public void AddModule(Component component)
        {
            components.Add(component);
            component.Added(this);
        }

        public void RemoveModule(Component module)
        {
            components.Remove(module);
            module.Removed(this);
        }
    }
}