using UnityEditor;
using UnityEngine;
using GameplayTags.Core;
using System.Linq;
using System.Collections.Generic;

namespace GameplayTags.Editor
{
    /// <summary>
    /// Custom property drawer for GameplayTag fields in the inspector
    /// </summary>
    [CustomPropertyDrawer(typeof(GameplayTag))]
    public class GameplayTagPropertyDrawer : PropertyDrawer
    {
        private const float BUTTON_WIDTH = 20f;
        private const float SPACING = 2f;
        internal static Dictionary<int, string> recentTags = new();
        private static readonly int MaxRecentTags = 5;
        
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);
            
            var tagNameProperty = property.FindPropertyRelative("tagName");
            var currentTagName = tagNameProperty.stringValue ?? "";
            
            // Calculate rects
            var labelRect = new Rect(position.x, position.y, EditorGUIUtility.labelWidth, position.height);
            var fieldRect = new Rect(
                position.x + EditorGUIUtility.labelWidth + SPACING, 
                position.y, 
                position.width - EditorGUIUtility.labelWidth - BUTTON_WIDTH - SPACING * 2, 
                position.height);
            var buttonRect = new Rect(
                position.x + position.width - BUTTON_WIDTH, 
                position.y, 
                BUTTON_WIDTH, 
                position.height);
            
            // Draw label
            EditorGUI.LabelField(labelRect, label);
            
            // Draw searchable dropdown button with enhanced options
            var displayText = string.IsNullOrEmpty(currentTagName) ? "None" : currentTagName;
            
            // Check if we have many tags - if so, use advanced popup
            var allTags = GetAllAvailableTags().Where(tag => !string.IsNullOrEmpty(tag)).ToArray();
            
            // Enhanced button styling
            var buttonStyle = new GUIStyle(EditorStyles.popup);
            if (string.IsNullOrEmpty(currentTagName))
            {
                buttonStyle.normal.textColor = Color.gray;
                buttonStyle.fontStyle = FontStyle.Italic;
            }
            
            if (Event.current.type == EventType.MouseDown && fieldRect.Contains(Event.current.mousePosition))
            {
                if (Event.current.button == 0) // Left click
                {
                    if (allTags.Length > 20) // Use searchable popup for many tags
                    {
                        TagSearchPopup.Show(fieldRect, property, tagNameProperty, allTags, currentTagName);
                    }
                    else
                    {
                        ShowTagSelectionDropdown(fieldRect, property, tagNameProperty);
                    }
                    Event.current.Use();
                }
            }
            
            // Draw the button with enhanced styling
            var buttonContent = new GUIContent(displayText, 
                string.IsNullOrEmpty(currentTagName) ? 
                "Click to select a gameplay tag" : 
                $"Current tag: {currentTagName}\nClick to change");
            GUI.Button(fieldRect, buttonContent, buttonStyle);
            
            // Draw tag editor button
            if (GUI.Button(buttonRect, "◦"))
            {
                GameplayTagEditor.ShowWindow();
            }
            
            EditorGUI.EndProperty();
        }
        
        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            return EditorGUIUtility.singleLineHeight;
        }
        
        private string[] GetAllAvailableTags()
        {
            var tags = new System.Collections.Generic.List<string> { "" }; // Empty option first
            
            // Get tags from GameplayTagManager if available
            if (Application.isPlaying && GameplayTagManager.Instance != null)
            {
                var registeredTags = GameplayTagManager.Instance.GetAllRegisteredTags()
                    .Select(tag => tag.TagName)
                    .Where(name => !string.IsNullOrEmpty(name))
                    .OrderBy(name => name);
                tags.AddRange(registeredTags);
            }
            else
            {
                // Get tags from settings assets
                var settingsGuids = AssetDatabase.FindAssets("t:GameplayTagSettings");
                foreach (var guid in settingsGuids)
                {
                    var path = AssetDatabase.GUIDToAssetPath(guid);
                    var settings = AssetDatabase.LoadAssetAtPath<GameplayTagSettings>(path);
                    if (settings != null)
                    {
                        var settingsTags = settings.DefaultTags
                            .Where(tag => !string.IsNullOrEmpty(tag) && !tags.Contains(tag))
                            .OrderBy(tag => tag);
                        tags.AddRange(settingsTags);
                    }
                }
            }
            
            return tags.Distinct().OrderBy(tag => tag).ToArray();
        }
        
        private void ShowTagSelectionDropdown(Rect buttonRect, SerializedProperty property, SerializedProperty tagNameProperty)
        {
            var allTags = GetAllAvailableTags().Where(tag => !string.IsNullOrEmpty(tag)).ToArray();
            var currentTag = tagNameProperty.stringValue ?? "";
            
            var menu = new GenericMenu();
            
            // Add None option
            menu.AddItem(new GUIContent("None"), string.IsNullOrEmpty(currentTag), () => {
                SetTagValue(property, tagNameProperty, "");
            });
            
            if (allTags.Length > 0)
            {
                menu.AddSeparator("");
                
                // Add recent tags section
                var recentTagsList = GetRecentTags().Where(tag => allTags.Contains(tag)).ToList();
                if (recentTagsList.Count > 0)
                {
                    foreach (var tag in recentTagsList)
                    {
                        var isSelected = tag == currentTag;
                        menu.AddItem(new GUIContent($"Recent/{tag}"), isSelected, () => {
                            SetTagValue(property, tagNameProperty, tag);
                            AddToRecentTags(tag);
                        });
                    }
                    menu.AddSeparator("");
                }
                
                // Group tags hierarchically and limit to prevent overwhelming menus
                var groupedTags = GroupTagsForMenu(allTags, currentTag);
                
                foreach (var kvp in groupedTags)
                {
                    var menuPath = kvp.Key;
                    var tag = kvp.Value;
                    var isSelected = tag == currentTag;
                    
                    menu.AddItem(new GUIContent(menuPath), isSelected, () => {
                        SetTagValue(property, tagNameProperty, tag);
                        AddToRecentTags(tag);
                    });
                }
            }
            
            menu.DropDown(buttonRect);
        }
        
        private void SetTagValue(SerializedProperty property, SerializedProperty tagNameProperty, string newTagName)
        {
            tagNameProperty.stringValue = newTagName;
            
            // Update cached hash
            var cachedHashProperty = property.FindPropertyRelative("cachedHash");
            if (cachedHashProperty != null)
            {
                cachedHashProperty.intValue = newTagName?.GetHashCode() ?? 0;
            }
            
            property.serializedObject.ApplyModifiedProperties();
        }
        
        private List<string> GetRecentTags()
        {
            var result = new List<string>();
            var propertyHash = GetPropertyHash();
            
            if (recentTags.ContainsKey(propertyHash))
            {
                var recentString = recentTags[propertyHash];
                if (!string.IsNullOrEmpty(recentString))
                {
                    result.AddRange(recentString.Split(',').Where(tag => !string.IsNullOrEmpty(tag)));
                }
            }
            
            return result;
        }
        
        private void AddToRecentTags(string tag)
        {
            if (string.IsNullOrEmpty(tag)) return;
            
            var propertyHash = GetPropertyHash();
            var recent = GetRecentTags();
            
            // Remove if already exists
            recent.Remove(tag);
            // Add to front
            recent.Insert(0, tag);
            // Limit size
            if (recent.Count > MaxRecentTags)
            {
                recent = recent.Take(MaxRecentTags).ToList();
            }
            
            recentTags[propertyHash] = string.Join(",", recent);
        }
        
        private int GetPropertyHash()
        {
            // Create a hash based on the property path to make recent tags per-property
            return fieldInfo.Name.GetHashCode();
        }
        
        private List<string> GroupTagsHierarchically(string[] tags)
        {
            // For now, just return the tags sorted
            // In the future, you could implement more sophisticated grouping
            return tags.OrderBy(tag => tag).ToList();
        }
        
        private Dictionary<string, string> GroupTagsForMenu(string[] tags, string currentTag)
        {
            var result = new Dictionary<string, string>();
            
            foreach (var tag in tags.OrderBy(t => t))
            {
                // Create hierarchical menu path
                var menuPath = tag;
                
                // Convert dots to slashes for menu hierarchy
                if (tag.Contains('.'))
                {
                    menuPath = tag.Replace('.', '/');
                }
                else if (tag.Contains('_'))
                {
                    // Handle underscore-separated tags
                    var parts = tag.Split('_');
                    if (parts.Length > 1)
                    {
                        menuPath = string.Join("/", parts);
                    }
                }
                
                result[menuPath] = tag;
            }
            
            return result;
        }
    }
    
    /// <summary>
    /// Searchable popup window for selecting gameplay tags
    /// </summary>
    public class TagSearchPopup : EditorWindow
    {
        private string searchText = "";
        private SerializedProperty targetProperty;
        private SerializedProperty tagNameProperty;
        private string[] allTags;
        private string currentTag;
        private Vector2 scrollPosition;
        private List<string> filteredTags;
        private int selectedIndex = -1;
        private int hoveredIndex = -1;
        private Dictionary<string, bool> expandedGroups = new();
        private bool showHierarchical = true;
        private bool focusSearchField = true;
        
        public static void Show(Rect buttonRect, SerializedProperty property, SerializedProperty tagNameProperty, string[] tags, string currentTag)
        {
            var popup = CreateInstance<TagSearchPopup>();
            popup.targetProperty = property;
            popup.tagNameProperty = tagNameProperty;
            popup.allTags = tags;
            popup.currentTag = currentTag;
            popup.searchText = "";
            popup.selectedIndex = -1;
            popup.hoveredIndex = -1;
            popup.showHierarchical = EditorPrefs.GetBool("GameplayTagPopup_ShowHierarchical", true);
            popup.expandedGroups = new Dictionary<string, bool>();
            popup.focusSearchField = true;
            popup.UpdateFilteredTags();
            
            // Convert button rect to screen coordinates properly
            var screenRect = GUIUtility.GUIToScreenRect(buttonRect);
            
            // Calculate popup size
            var popupWidth = Mathf.Max(350, screenRect.width);
            var popupHeight = 400;
            
            // Start with position below the button
            var windowRect = new Rect(screenRect.x, screenRect.y + screenRect.height, popupWidth, popupHeight);
            
            // Get the current editor window bounds to keep popup on same screen
            var currentWindow = EditorWindow.focusedWindow;
            if (currentWindow != null)
            {
                var editorRect = currentWindow.position;
                
                // Adjust if popup would go off the right edge of the editor window
                if (windowRect.x + windowRect.width > editorRect.x + editorRect.width)
                {
                    windowRect.x = (editorRect.x + editorRect.width) - windowRect.width;
                }
                
                // Adjust if popup would go off the bottom edge of the editor window
                if (windowRect.y + windowRect.height > editorRect.y + editorRect.height)
                {
                    windowRect.y = screenRect.y - windowRect.height; // Show above the button instead
                }
                
                // Ensure popup doesn't go above the top of the editor window
                if (windowRect.y < editorRect.y)
                {
                    windowRect.y = editorRect.y;
                    windowRect.height = Mathf.Min(popupHeight, editorRect.height - 50); // Leave some margin
                }
                
                // Ensure popup doesn't go left of the editor window
                if (windowRect.x < editorRect.x)
                {
                    windowRect.x = editorRect.x;
                }
            }
            
            popup.position = windowRect;
            popup.ShowPopup();
            popup.Focus();
        }
        
        private void OnGUI()
        {
            // Handle escape key
            if (Event.current.type == EventType.KeyDown && Event.current.keyCode == KeyCode.Escape)
            {
                Close();
                return;
            }
            
            // Custom styling
            var headerStyle = new GUIStyle(EditorStyles.toolbar) { fontStyle = FontStyle.Bold };
            var searchStyle = new GUIStyle(EditorStyles.toolbarSearchField);
            
            EditorGUILayout.BeginVertical();
            
            // Header with title and options
            EditorGUILayout.BeginHorizontal(headerStyle);
            GUILayout.Label("Select Gameplay Tag", EditorStyles.boldLabel);
            GUILayout.FlexibleSpace();
            
            // Toggle hierarchical view
            EditorGUI.BeginChangeCheck();
            showHierarchical = GUILayout.Toggle(showHierarchical, "Tree", EditorStyles.toolbarButton, GUILayout.Width(40));
            if (EditorGUI.EndChangeCheck())
            {
                UpdateFilteredTags();
            }
            
            EditorGUILayout.EndHorizontal();
            
            // Search field with better styling
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label("Search:", GUILayout.Width(50));
            
            // Auto-focus search field on first frame
            if (focusSearchField)
            {
                GUI.SetNextControlName("SearchField");
                focusSearchField = false;
                EditorGUI.FocusTextInControl("SearchField");
            }
            
            EditorGUI.BeginChangeCheck();
            var newSearchText = EditorGUILayout.TextField(searchText, searchStyle);
            if (EditorGUI.EndChangeCheck())
            {
                searchText = newSearchText;
                selectedIndex = -1;
                hoveredIndex = -1;
                UpdateFilteredTags();
            }
            
            // Clear search button
            if (!string.IsNullOrEmpty(searchText) && GUILayout.Button("×", EditorStyles.toolbarButton, GUILayout.Width(20)))
            {
                searchText = "";
                selectedIndex = -1;
                hoveredIndex = -1;
                UpdateFilteredTags();
                GUI.SetNextControlName("SearchField");
                EditorGUI.FocusTextInControl("SearchField");
            }
            EditorGUILayout.EndHorizontal();
            
            // Show search results count
            if (!string.IsNullOrEmpty(searchText))
            {
                var resultText = filteredTags.Count == 1 ? "1 result" : $"{filteredTags.Count} results";
                EditorGUILayout.LabelField($"🔍 {resultText}", EditorStyles.miniLabel);
            }
            
            // Handle keyboard navigation and mouse tracking
            HandleInput();
            
            EditorGUILayout.Space(2);
            
            // Tags list with enhanced visuals
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUIStyle.none, GUI.skin.verticalScrollbar);
            
            DrawTagsList();
            
            EditorGUILayout.EndScrollView();
            
            // Status bar with helpful information
            EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
            
            var statusText = "";
            if (!string.IsNullOrEmpty(searchText))
            {
                statusText = filteredTags.Count == 0 ? "Press Enter to create new tag" : "Tab to autocomplete • Enter to select";
            }
            else
            {
                statusText = $"{allTags.Length} tags • Arrow keys to navigate • Esc to close";
            }
            
            EditorGUILayout.LabelField(statusText, EditorStyles.miniLabel);
            GUILayout.FlexibleSpace();
            
            // Show current selection info
            if (selectedIndex >= 0 && selectedIndex < filteredTags.Count)
            {
                EditorGUILayout.LabelField($"Selected: {filteredTags[selectedIndex]}", EditorStyles.miniLabel);
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
        }
        
        private void HandleKeyboardInput()
        {
            if (Event.current.type == EventType.KeyDown)
            {
                switch (Event.current.keyCode)
                {
                    case KeyCode.DownArrow:
                        selectedIndex = Mathf.Min(selectedIndex + 1, filteredTags.Count - 1);
                        ScrollToSelected();
                        Event.current.Use();
                        Repaint();
                        break;
                        
                    case KeyCode.UpArrow:
                        selectedIndex = Mathf.Max(selectedIndex - 1, -1);
                        ScrollToSelected();
                        Event.current.Use();
                        Repaint();
                        break;
                        
                    case KeyCode.Return:
                    case KeyCode.KeypadEnter:
                        if (selectedIndex >= 0 && selectedIndex < filteredTags.Count)
                        {
                            SelectTag(filteredTags[selectedIndex]);
                        }
                        else if (selectedIndex == -1 && !string.IsNullOrEmpty(searchText))
                        {
                            // Create new tag if exact match not found
                            SelectTag(searchText);
                        }
                        else if (filteredTags.Count > 0)
                        {
                            // Select first result if nothing selected
                            SelectTag(filteredTags[0]);
                        }
                        Event.current.Use();
                        break;
                        
                    case KeyCode.Tab:
                        // Auto-complete with first result
                        if (filteredTags.Count > 0 && !string.IsNullOrEmpty(searchText))
                        {
                            var firstResult = filteredTags[0];
                            if (firstResult.StartsWith(searchText, System.StringComparison.OrdinalIgnoreCase))
                            {
                                searchText = firstResult;
                                selectedIndex = 0;
                                UpdateFilteredTags();
                            }
                        }
                        Event.current.Use();
                        break;
                        
                    case KeyCode.PageDown:
                        selectedIndex = Mathf.Min(selectedIndex + 10, filteredTags.Count - 1);
                        ScrollToSelected();
                        Event.current.Use();
                        Repaint();
                        break;
                        
                    case KeyCode.PageUp:
                        selectedIndex = Mathf.Max(selectedIndex - 10, -1);
                        ScrollToSelected();
                        Event.current.Use();
                        Repaint();
                        break;
                        
                    case KeyCode.Home:
                        selectedIndex = 0;
                        ScrollToSelected();
                        Event.current.Use();
                        Repaint();
                        break;
                        
                    case KeyCode.End:
                        selectedIndex = filteredTags.Count - 1;
                        ScrollToSelected();
                        Event.current.Use();
                        Repaint();
                        break;
                }
            }
        }
        
        private void HandleInput()
        {
            HandleKeyboardInput();
            
            // Track mouse position for hover effects
            if (Event.current.type == EventType.MouseMove)
            {
                Repaint();
            }
        }
        
        private void ScrollToSelected()
        {
            if (selectedIndex >= 0 && selectedIndex < filteredTags.Count)
            {
                var itemHeight = 22f;
                var targetY = selectedIndex * itemHeight;
                var viewHeight = position.height - 100; // Account for header/search area
                
                if (targetY < scrollPosition.y)
                {
                    scrollPosition.y = targetY;
                }
                else if (targetY > scrollPosition.y + viewHeight - itemHeight)
                {
                    scrollPosition.y = targetY - viewHeight + itemHeight;
                }
            }
        }
        
        private void DrawTagsList()
        {
            var itemHeight = 22f;
            var indent = 0f;
            
            // None option with enhanced styling
            DrawTagItem("None", "", -1, itemHeight, indent, string.IsNullOrEmpty(currentTag));
            
            // Show recent tags section if no search and we have recent tags
            if (string.IsNullOrEmpty(searchText))
            {
                var recentTagsList = GetRecentTagsList().Where(tag => System.Array.Exists(allTags, t => t == tag)).ToList();
                if (recentTagsList.Count > 0)
                {
                    // Recent tags header
                    var headerRect = GUILayoutUtility.GetRect(GUIContent.none, GUIStyle.none, GUILayout.Height(20));
                    var headerStyle = new GUIStyle(EditorStyles.boldLabel)
                    {
                        fontSize = 10,
                        normal = { textColor = EditorGUIUtility.isProSkin ? Color.gray : new Color(0.4f, 0.4f, 0.4f) }
                    };
                    GUI.Label(headerRect, "RECENT", headerStyle);
                    
                    // Recent tags
                    foreach (var tag in recentTagsList)
                    {
                        var index = System.Array.IndexOf(allTags, tag);
                        DrawTagItem($"🕐 {tag}", tag, index, itemHeight, indent, tag == currentTag);
                    }
                    
                    // Separator
                    EditorGUILayout.Space(4);
                    var separatorRect = GUILayoutUtility.GetRect(GUIContent.none, GUIStyle.none, GUILayout.Height(1));
                    EditorGUI.DrawRect(separatorRect, EditorGUIUtility.isProSkin ? new Color(0.3f, 0.3f, 0.3f) : new Color(0.7f, 0.7f, 0.7f));
                    EditorGUILayout.Space(4);
                    
                    // All tags header
                    var allHeaderRect = GUILayoutUtility.GetRect(GUIContent.none, GUIStyle.none, GUILayout.Height(20));
                    GUI.Label(allHeaderRect, "ALL TAGS", headerStyle);
                }
            }
            
            if (!showHierarchical || !string.IsNullOrEmpty(searchText))
            {
                // Flat list view
                for (var i = 0; i < filteredTags.Count; i++)
                {
                    var tag = filteredTags[i];
                    DrawTagItem(tag, tag, i, itemHeight, indent, tag == currentTag);
                }
            }
            else
            {
                // Hierarchical tree view
                DrawHierarchicalTags(itemHeight);
            }
            
            // Show "no results" message if search returned nothing
            if (!string.IsNullOrEmpty(searchText) && filteredTags.Count == 0)
            {
                var noResultsRect = GUILayoutUtility.GetRect(GUIContent.none, GUIStyle.none, GUILayout.Height(30));
                var noResultsStyle = new GUIStyle(EditorStyles.centeredGreyMiniLabel)
                {
                    fontSize = 12,
                    normal = { textColor = Color.gray }
                };
                GUI.Label(noResultsRect, "No tags found. Press Enter to create a new tag.", noResultsStyle);
            }
        }
        
        private void DrawTagItem(string displayName, string tagValue, int index, float height, float indentLevel, bool isCurrentTag)
        {
            var rect = GUILayoutUtility.GetRect(GUIContent.none, GUIStyle.none, GUILayout.Height(height));
            var isHovered = rect.Contains(Event.current.mousePosition);
            var isKeyboardNavigated = index >= 0 && index == selectedIndex;
            
            // Update hovered index
            if (isHovered && Event.current.type == EventType.MouseMove)
            {
                hoveredIndex = index;
            }
            
            // Background colors
            var backgroundColor = Color.clear;
            if (isCurrentTag)
            {
                backgroundColor = EditorGUIUtility.isProSkin ? new Color(0.3f, 0.5f, 0.8f, 0.6f) : new Color(0.2f, 0.4f, 0.8f, 0.4f);
            }
            else if (isKeyboardNavigated)
            {
                backgroundColor = EditorGUIUtility.isProSkin ? new Color(0.3f, 0.5f, 0.8f, 0.4f) : new Color(0.2f, 0.4f, 0.8f, 0.3f);
            }
            else if (isHovered)
            {
                backgroundColor = EditorGUIUtility.isProSkin ? new Color(1f, 1f, 1f, 0.1f) : new Color(0f, 0f, 0f, 0.1f);
            }
            
            if (backgroundColor != Color.clear)
            {
                EditorGUI.DrawRect(rect, backgroundColor);
            }
            
            // Content area with indent
            var contentRect = new Rect(rect.x + indentLevel, rect.y, rect.width - indentLevel - 20, rect.height);
            
            // Text styling
            var style = new GUIStyle(EditorStyles.label)
            {
                fontStyle = isCurrentTag ? FontStyle.Bold : FontStyle.Normal,
                normal = { textColor = EditorGUIUtility.isProSkin ? Color.white : Color.black }
            };
            
            if (string.IsNullOrEmpty(tagValue))
            {
                style.fontStyle = FontStyle.Italic;
                style.normal.textColor = EditorGUIUtility.isProSkin ? Color.gray : Color.gray;
            }
            
            // Highlight search terms
            var displayText = displayName;
            if (!string.IsNullOrEmpty(searchText) && !string.IsNullOrEmpty(tagValue))
            {
                var searchIndex = displayName.IndexOf(searchText, System.StringComparison.OrdinalIgnoreCase);
                if (searchIndex >= 0)
                {
                    // We could highlight here, but for simplicity, just show the text
                    displayText = displayName;
                }
            }
            
            // Draw the text
            GUI.Label(contentRect, displayText, style);
            
            // Show tag creation hint for new tags
            if (!string.IsNullOrEmpty(searchText) && tagValue == searchText && !System.Array.Exists(allTags, t => t == searchText))
            {
                var hintRect = new Rect(rect.x + rect.width - 60, rect.y, 55, rect.height);
                var hintStyle = new GUIStyle(EditorStyles.miniLabel)
                {
                    normal = { textColor = EditorGUIUtility.isProSkin ? Color.green : new Color(0, 0.6f, 0) }
                };
                GUI.Label(hintRect, "(new)", hintStyle);
            }
            
            // Handle click for immediate selection
            if (Event.current.type == EventType.MouseDown && rect.Contains(Event.current.mousePosition))
            {
                SelectTag(tagValue);
                Event.current.Use();
            }
        }
        
        private void DrawHierarchicalTags(float itemHeight)
        {
            var hierarchyData = BuildTagHierarchy(filteredTags.ToArray());
            DrawHierarchyLevel(hierarchyData, 0, itemHeight);
        }
        
        private void DrawHierarchyLevel(List<TagHierarchyNode> nodes, int depth, float itemHeight)
        {
            var indent = depth * 16f;
            
            foreach (var node in nodes.OrderBy(n => n.Name))
            {
                if (node.IsLeaf)
                {
                    // Leaf node - just a selectable tag
                    var index = filteredTags.IndexOf(node.FullTag);
                    DrawTagItem(node.Name, node.FullTag, index, itemHeight, indent, node.FullTag == currentTag);
                }
                else
                {
                    // Parent node - draw collapsible header
                    var isExpanded = expandedGroups.GetValueOrDefault(node.Path, true);
                    DrawGroupHeader(node.Name, node.Path, node.FullTag, itemHeight, indent, isExpanded);
                    
                    // Draw children if expanded
                    if (isExpanded && node.Children.Count > 0)
                    {
                        DrawHierarchyLevel(node.Children, depth + 1, itemHeight);
                    }
                }
            }
        }
        
        private void DrawGroupHeader(string displayName, string path, string fullTag, float height, float indentLevel, bool isExpanded)
        {
            var rect = GUILayoutUtility.GetRect(GUIContent.none, GUIStyle.none, GUILayout.Height(height));
            var isHovered = rect.Contains(Event.current.mousePosition);
            var isCurrentTag = fullTag == currentTag;
            var isSelectableTag = !string.IsNullOrEmpty(fullTag); // All parent nodes with tags are selectable
            
            // Background for group headers
            var backgroundColor = Color.clear;
            if (isCurrentTag)
            {
                backgroundColor = EditorGUIUtility.isProSkin ? new Color(0.3f, 0.5f, 0.8f, 0.6f) : new Color(0.2f, 0.4f, 0.8f, 0.4f);
            }
            else if (isHovered)
            {
                backgroundColor = EditorGUIUtility.isProSkin ? new Color(1f, 1f, 1f, 0.05f) : new Color(0f, 0f, 0f, 0.05f);
            }
            
            if (backgroundColor != Color.clear)
            {
                EditorGUI.DrawRect(rect, backgroundColor);
            }
            
            // Foldout arrow - only clickable area for expand/collapse
            var arrowRect = new Rect(rect.x + indentLevel + 2, rect.y + (rect.height - 12) / 2, 12, 12);
            var arrow = isExpanded ? "▼" : "▶";
            var arrowStyle = new GUIStyle(EditorStyles.label) { fontSize = 8 };
            GUI.Label(arrowRect, arrow, arrowStyle);
            
            // Group name - clickable area for selection
            var labelRect = new Rect(rect.x + indentLevel + 16, rect.y, rect.width - indentLevel - 16 - 20, rect.height);
            var style = new GUIStyle(EditorStyles.label) 
            { 
                fontStyle = isCurrentTag ? FontStyle.Bold : FontStyle.Bold,
                normal = { textColor = EditorGUIUtility.isProSkin ? new Color(0.8f, 0.8f, 0.8f) : new Color(0.3f, 0.3f, 0.3f) }
            };
            
            if (isCurrentTag)
            {
                style.normal.textColor = EditorGUIUtility.isProSkin ? Color.white : Color.black;
            }
            else if (isSelectableTag)
            {
                // Make all parent tags selectable and prominent
                style.normal.textColor = EditorGUIUtility.isProSkin ? new Color(0.9f, 0.9f, 0.9f) : new Color(0.2f, 0.2f, 0.2f);
            }
            
            GUI.Label(labelRect, displayName, style);
            
            // Show selection indicator for all parent tags
            if (isSelectableTag)
            {
                var indicatorRect = new Rect(rect.x + rect.width - 18, rect.y + (rect.height - 12) / 2, 12, 12);
                var indicatorStyle = new GUIStyle(EditorStyles.label) 
                { 
                    fontSize = 8,
                    normal = { textColor = EditorGUIUtility.isProSkin ? Color.cyan : new Color(0, 0.6f, 0.8f) }
                };
                GUI.Label(indicatorRect, "●", indicatorStyle);
            }
            
            // Handle clicks
            if (Event.current.type == EventType.MouseDown && rect.Contains(Event.current.mousePosition))
            {
                if (arrowRect.Contains(Event.current.mousePosition))
                {
                    // Click on arrow - toggle expand/collapse
                    expandedGroups[path] = !isExpanded;
                    Event.current.Use();
                    Repaint();
                }
                else if (labelRect.Contains(Event.current.mousePosition) && isSelectableTag)
                {
                    // Click on name - immediately select the tag
                    SelectTag(fullTag);
                    Event.current.Use();
                }
            }
        }
        
        private Dictionary<string, List<string>> GroupTagsForHierarchy(string[] tags)
        {
            var groups = new Dictionary<string, List<string>>();
            
            foreach (var tag in tags)
            {
                var parts = tag.Split('.', '_');
                if (parts.Length > 1)
                {
                    var groupName = parts[0];
                    if (!groups.ContainsKey(groupName))
                    {
                        groups[groupName] = new List<string>();
                    }
                    groups[groupName].Add(tag);
                }
                else
                {
                    // Single item group
                    groups[tag] = new List<string> { tag };
                }
            }
            
            return groups;
        }
        
        private class TagHierarchyNode
        {
            public string Name { get; set; }
            public string Path { get; set; }
            public string FullTag { get; set; }
            public List<TagHierarchyNode> Children { get; set; } = new();
            public bool IsLeaf => Children.Count == 0;
        }
        
        private List<TagHierarchyNode> BuildTagHierarchy(string[] tags)
        {
            var root = new TagHierarchyNode { Name = "Root", Path = "", FullTag = "" };
            var nodeMap = new Dictionary<string, TagHierarchyNode>();
            nodeMap[""] = root;
            
            foreach (var tag in tags)
            {
                // Support multiple separators: dots, underscores, slashes
                var parts = tag.Split(new char[] { '.', '_', '/' }, System.StringSplitOptions.RemoveEmptyEntries);
                var currentPath = "";
                var currentNode = root;
                
                for (var i = 0; i < parts.Length; i++)
                {
                    var part = parts[i];
                    var separator = GetOriginalSeparator(tag, parts, i);
                    var newPath = string.IsNullOrEmpty(currentPath) ? part : $"{currentPath}{separator}{part}";
                    
                    if (!nodeMap.ContainsKey(newPath))
                    {
                        var newNode = new TagHierarchyNode
                        {
                            Name = part,
                            Path = newPath,
                            FullTag = newPath // All paths should be selectable as tags
                        };
                        
                        nodeMap[newPath] = newNode;
                        currentNode.Children.Add(newNode);
                    }
                    
                    currentNode = nodeMap[newPath];
                    currentPath = newPath;
                }
                
                // Ensure the final node has the correct full tag (in case it's different from the path)
                currentNode.FullTag = tag;
            }
            
            return root.Children;
        }
        
        private char GetOriginalSeparator(string originalTag, string[] parts, int partIndex)
        {
            if (partIndex == 0) return '.'; // Default for root
            
            // Find the separator that was used at this position in the original tag
            var reconstructed = string.Join(".", parts.Take(partIndex + 1));
            var currentPos = parts.Take(partIndex).Sum(p => p.Length) + partIndex;
            
            if (currentPos > 0 && currentPos < originalTag.Length)
            {
                return originalTag[currentPos - 1];
            }
            
            // Fallback to detecting most common separator in the tag
            if (originalTag.Contains('.')) return '.';
            if (originalTag.Contains('_')) return '_';
            if (originalTag.Contains('/')) return '/';
            
            return '.'; // Default
        }
        
        private List<string> GetRecentTagsList()
        {
            // Get recent tags from the property drawer's static cache
            var propertyHash = targetProperty?.FindPropertyRelative("tagName")?.propertyPath?.GetHashCode() ?? 0;
            if (GameplayTagPropertyDrawer.recentTags.ContainsKey(propertyHash))
            {
                var recentString = GameplayTagPropertyDrawer.recentTags[propertyHash];
                if (!string.IsNullOrEmpty(recentString))
                {
                    return recentString.Split(',').Where(tag => !string.IsNullOrEmpty(tag)).ToList();
                }
            }
            return new List<string>();
        }
        
        private void UpdateFilteredTags()
        {
            if (string.IsNullOrEmpty(searchText))
            {
                filteredTags = allTags.ToList();
            }
            else
            {
                filteredTags = allTags
                    .Where(tag => tag.ToLower().Contains(searchText.ToLower()))
                    .OrderBy(tag => 
                    {
                        // Prioritize exact matches, then starts with, then contains
                        if (tag.Equals(searchText, System.StringComparison.OrdinalIgnoreCase))
                            return 0;
                        if (tag.StartsWith(searchText, System.StringComparison.OrdinalIgnoreCase))
                            return 1;
                        return 2;
                    })
                    .ThenBy(tag => tag)
                    .ToList();
            }
        }
        
        private void SelectTag(string tag)
        {
            tagNameProperty.stringValue = tag;
            
            // Update cached hash
            var cachedHashProperty = targetProperty.FindPropertyRelative("cachedHash");
            if (cachedHashProperty != null)
            {
                cachedHashProperty.intValue = tag?.GetHashCode() ?? 0;
            }
            
            // Add to recent tags
            if (!string.IsNullOrEmpty(tag))
            {
                var propertyHash = targetProperty?.FindPropertyRelative("tagName")?.propertyPath?.GetHashCode() ?? 0;
                var recent = GetRecentTagsList();
                recent.Remove(tag);
                recent.Insert(0, tag);
                if (recent.Count > 5) recent = recent.Take(5).ToList();
                GameplayTagPropertyDrawer.recentTags[propertyHash] = string.Join(",", recent);
            }
            
            // Save preferences
            EditorPrefs.SetBool("GameplayTagPopup_ShowHierarchical", showHierarchical);
            
            targetProperty.serializedObject.ApplyModifiedProperties();
            Close();
        }
        
        private void OnLostFocus()
        {
            Close();
        }
    }
}
