using UnityEditor;
using UnityEngine;
using GameplayTags.Core;

namespace GameplayTags.Editor
{
    /// <summary>
    /// Custom inspector for GameplayTagSettings
    /// </summary>
    [CustomEditor(typeof(GameplayTagSettings))]
    public class GameplayTagSettingsInspector : UnityEditor.Editor
    {
        private SerializedProperty defaultTagsProperty;
        private SerializedProperty autoRegisterProperty;
        private SerializedProperty validateTagsProperty;
        private SerializedProperty allowDynamicProperty;
        
        private void OnEnable()
        {
            defaultTagsProperty = serializedObject.FindProperty("defaultTags");
            autoRegisterProperty = serializedObject.FindProperty("autoRegisterOnAwake");
            validateTagsProperty = serializedObject.FindProperty("validateTagsOnBuild");
            allowDynamicProperty = serializedObject.FindProperty("allowDynamicTagRegistration");
        }
        
        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            
            var settings = (GameplayTagSettings)target;
            
            // Header
            EditorGUILayout.Space();
            var headerStyle = new GUIStyle(EditorStyles.boldLabel) { fontSize = 16 };
            EditorGUILayout.LabelField("Gameplay Tag Settings", headerStyle);
            EditorGUILayout.Space();
            
            // Open Editor Button
            if (GUILayout.Button("Open Tag Editor", GUILayout.Height(30)))
            {
                GameplayTagEditor.ShowWindow();
            }
            
            EditorGUILayout.Space();
            
            // Settings
            EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(autoRegisterProperty, new GUIContent("Auto Register on Awake", 
                "Automatically register all default tags when the GameplayTagManager awakens"));
            EditorGUILayout.PropertyField(validateTagsProperty, new GUIContent("Validate Tags on Build", 
                "Validate tag integrity during build process"));
            EditorGUILayout.PropertyField(allowDynamicProperty, new GUIContent("Allow Dynamic Registration", 
                "Allow runtime registration of new tags not in the default list"));
            
            EditorGUILayout.Space();
            
            // Tags section
            EditorGUILayout.LabelField("Default Tags", EditorStyles.boldLabel);
            
            if (settings.DefaultTags.Count == 0)
            {
                EditorGUILayout.HelpBox("No default tags defined. Use the Tag Editor to add tags.", MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox($"Total Tags: {settings.DefaultTags.Count}", MessageType.Info);
                
                // Show first few tags as preview
                var previewCount = Mathf.Min(5, settings.DefaultTags.Count);
                for (var i = 0; i < previewCount; i++)
                {
                    EditorGUILayout.LabelField($"• {settings.DefaultTags[i]}", EditorStyles.miniLabel);
                }
                
                if (settings.DefaultTags.Count > previewCount)
                {
                    EditorGUILayout.LabelField($"... and {settings.DefaultTags.Count - previewCount} more", EditorStyles.miniLabel);
                }
            }
            
            EditorGUILayout.Space();
            
            // Actions
            EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);
            
            using (new EditorGUILayout.HorizontalScope())
            {
                if (GUILayout.Button("Validate & Sort"))
                {
                    Undo.RecordObject(settings, "Validate and Sort Tags");
                    settings.ValidateAndSortTags();
                }
                
                if (GUILayout.Button("Apply to Manager"))
                {
                    var manager = GameplayTagManager.Instance;
                    if (manager != null)
                    {
                        manager.RegisterTags(settings.DefaultTags);
                        Debug.Log($"Applied {settings.DefaultTags.Count} tags to GameplayTagManager");
                    }
                    else
                    {
                        Debug.LogWarning("GameplayTagManager not found in scene");
                    }
                }
            }
            
            if (GUILayout.Button("Clear All Tags"))
            {
                if (EditorUtility.DisplayDialog("Clear All Tags", 
                    "Are you sure you want to remove all tags? This action cannot be undone.", 
                    "Yes", "Cancel"))
                {
                    Undo.RecordObject(settings, "Clear All Tags");
                    settings.ClearDefaultTags();
                }
            }
            
            EditorGUILayout.Space();
            
            // Advanced section (show the raw list if needed)
            var showAdvanced = EditorPrefs.GetBool("GameplayTagSettings.ShowAdvanced", false);
            showAdvanced = EditorGUILayout.Foldout(showAdvanced, "Advanced (Raw Tag List)", true);
            EditorPrefs.SetBool("GameplayTagSettings.ShowAdvanced", showAdvanced);
            
            if (showAdvanced)
            {
                EditorGUILayout.HelpBox("Warning: Editing this list directly may cause issues. Use the Tag Editor instead.", MessageType.Warning);
                EditorGUILayout.PropertyField(defaultTagsProperty, true);
            }
            
            serializedObject.ApplyModifiedProperties();
        }
    }
}
