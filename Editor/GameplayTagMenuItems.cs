using UnityEditor;
using UnityEngine;
using GameplayTags.Core;

namespace GameplayTags.Editor
{
    /// <summary>
    /// Menu items for gameplay tag system
    /// </summary>
    public static class GameplayTagMenuItems
    {
        [MenuItem("Assets/Create/Gameplay Tags/Tag Settings (with Examples)", priority = 2)]
        public static void CreateTagSettingsWithExamples()
        {
            var settings = ScriptableObject.CreateInstance<GameplayTagSettings>();
            
            // Add some basic example tags
            settings.AddDefaultTag("Player.State.Idle");
            settings.AddDefaultTag("Player.State.Moving");
            settings.AddDefaultTag("Player.State.Jumping");
            settings.AddDefaultTag("Player.Ability.Jump");
            settings.AddDefaultTag("Player.Ability.Dash");
            settings.AddDefaultTag("Enemy.Type.Basic");
            settings.AddDefaultTag("Enemy.Type.Boss");
            settings.AddDefaultTag("Combat.Damage.Physical");
            settings.AddDefaultTag("Combat.Damage.Magical");
            settings.AddDefaultTag("UI.Menu.MainMenu");
            settings.AddDefaultTag("UI.Menu.PauseMenu");
            
            // Save the asset
            var path = "Assets/GameplayTagSettings_Examples.asset";
            path = AssetDatabase.GenerateUniqueAssetPath(path);
            
            AssetDatabase.CreateAsset(settings, path);
            AssetDatabase.SaveAssets();
            
            // Select the created asset
            Selection.activeObject = settings;
            EditorGUIUtility.PingObject(settings);
            
            Debug.Log($"Created GameplayTagSettings at {path} with example tags");
        }
        
        [MenuItem("Assets/Create/Gameplay Tags/Tag Settings (Comprehensive)", priority = 3)]
        public static void CreateComprehensiveTagSettings()
        {
            var settings = ScriptableObject.CreateInstance<GameplayTagSettings>();
            
            // Player System Tags
            AddPlayerTags(settings);
            
            // Enemy System Tags
            AddEnemyTags(settings);
            
            // Combat System Tags
            AddCombatTags(settings);
            
            // Movement System Tags
            AddMovementTags(settings);
            
            // Status Effect Tags
            AddStatusEffectTags(settings);
            
            // Item and Equipment Tags
            AddItemTags(settings);
            
            // Environment Tags
            AddEnvironmentTags(settings);
            
            // UI System Tags
            AddUITags(settings);
            
            // Audio System Tags
            AddAudioTags(settings);
            
            // Game State Tags
            AddGameStateTags(settings);
            
            // Save the asset
            var path = "Assets/GameplayTagSettings_Comprehensive.asset";
            path = AssetDatabase.GenerateUniqueAssetPath(path);
            
            AssetDatabase.CreateAsset(settings, path);
            AssetDatabase.SaveAssets();
            
            // Select the created asset
            Selection.activeObject = settings;
            EditorGUIUtility.PingObject(settings);
            
            Debug.Log($"Created comprehensive GameplayTagSettings at {path} with {settings.DefaultTags.Count} tags");
        }
        
        private static void AddPlayerTags(GameplayTagSettings settings)
        {
            // Player States
            settings.AddDefaultTag("Player.State.Idle");
            settings.AddDefaultTag("Player.State.Moving");
            settings.AddDefaultTag("Player.State.Running");
            settings.AddDefaultTag("Player.State.Jumping");
            settings.AddDefaultTag("Player.State.Falling");
            settings.AddDefaultTag("Player.State.Climbing");
            settings.AddDefaultTag("Player.State.Swimming");
            settings.AddDefaultTag("Player.State.Crouching");
            settings.AddDefaultTag("Player.State.Sliding");
            settings.AddDefaultTag("Player.State.WallSliding");
            settings.AddDefaultTag("Player.State.Dashing");
            settings.AddDefaultTag("Player.State.Dead");
            settings.AddDefaultTag("Player.State.Respawning");
            
            // Player Abilities
            settings.AddDefaultTag("Player.Ability.Jump");
            settings.AddDefaultTag("Player.Ability.DoubleJump");
            settings.AddDefaultTag("Player.Ability.WallJump");
            settings.AddDefaultTag("Player.Ability.Dash");
            settings.AddDefaultTag("Player.Ability.AirDash");
            settings.AddDefaultTag("Player.Ability.Glide");
            settings.AddDefaultTag("Player.Ability.Grapple");
            settings.AddDefaultTag("Player.Ability.Teleport");
            settings.AddDefaultTag("Player.Ability.Sprint");
            settings.AddDefaultTag("Player.Ability.Stealth");
            
            // Player Attributes
            settings.AddDefaultTag("Player.Attribute.Health");
            settings.AddDefaultTag("Player.Attribute.Mana");
            settings.AddDefaultTag("Player.Attribute.Stamina");
            settings.AddDefaultTag("Player.Attribute.Strength");
            settings.AddDefaultTag("Player.Attribute.Defense");
            settings.AddDefaultTag("Player.Attribute.Speed");
            settings.AddDefaultTag("Player.Attribute.Intelligence");
            settings.AddDefaultTag("Player.Attribute.Luck");
        }
        
        private static void AddEnemyTags(GameplayTagSettings settings)
        {
            // Enemy Types
            settings.AddDefaultTag("Enemy.Type.Melee");
            settings.AddDefaultTag("Enemy.Type.Ranged");
            settings.AddDefaultTag("Enemy.Type.Flying");
            settings.AddDefaultTag("Enemy.Type.Boss");
            settings.AddDefaultTag("Enemy.Type.Miniboss");
            settings.AddDefaultTag("Enemy.Type.Elite");
            settings.AddDefaultTag("Enemy.Type.Swarm");
            settings.AddDefaultTag("Enemy.Type.Tank");
            settings.AddDefaultTag("Enemy.Type.Assassin");
            settings.AddDefaultTag("Enemy.Type.Caster");
            
            // Enemy Behaviors
            settings.AddDefaultTag("Enemy.Behavior.Aggressive");
            settings.AddDefaultTag("Enemy.Behavior.Passive");
            settings.AddDefaultTag("Enemy.Behavior.Patrol");
            settings.AddDefaultTag("Enemy.Behavior.Guard");
            settings.AddDefaultTag("Enemy.Behavior.Flee");
            settings.AddDefaultTag("Enemy.Behavior.Hunt");
            settings.AddDefaultTag("Enemy.Behavior.Ambush");
            
            // Enemy States
            settings.AddDefaultTag("Enemy.State.Idle");
            settings.AddDefaultTag("Enemy.State.Patrolling");
            settings.AddDefaultTag("Enemy.State.Chasing");
            settings.AddDefaultTag("Enemy.State.Attacking");
            settings.AddDefaultTag("Enemy.State.Stunned");
            settings.AddDefaultTag("Enemy.State.Fleeing");
            settings.AddDefaultTag("Enemy.State.Dead");
        }
        
        private static void AddCombatTags(GameplayTagSettings settings)
        {
            // Damage Types
            settings.AddDefaultTag("Combat.Damage.Physical");
            settings.AddDefaultTag("Combat.Damage.Magical");
            settings.AddDefaultTag("Combat.Damage.Fire");
            settings.AddDefaultTag("Combat.Damage.Ice");
            settings.AddDefaultTag("Combat.Damage.Lightning");
            settings.AddDefaultTag("Combat.Damage.Poison");
            settings.AddDefaultTag("Combat.Damage.Holy");
            settings.AddDefaultTag("Combat.Damage.Dark");
            settings.AddDefaultTag("Combat.Damage.Piercing");
            settings.AddDefaultTag("Combat.Damage.Slashing");
            settings.AddDefaultTag("Combat.Damage.Bludgeoning");
            settings.AddDefaultTag("Combat.Damage.True");
            
            // Weapon Types
            settings.AddDefaultTag("Combat.Weapon.Sword");
            settings.AddDefaultTag("Combat.Weapon.Bow");
            settings.AddDefaultTag("Combat.Weapon.Staff");
            settings.AddDefaultTag("Combat.Weapon.Dagger");
            settings.AddDefaultTag("Combat.Weapon.Axe");
            settings.AddDefaultTag("Combat.Weapon.Hammer");
            settings.AddDefaultTag("Combat.Weapon.Spear");
            settings.AddDefaultTag("Combat.Weapon.Shield");
            settings.AddDefaultTag("Combat.Weapon.Gun");
            settings.AddDefaultTag("Combat.Weapon.Grenade");
            
            // Combat Actions
            settings.AddDefaultTag("Combat.Action.Attack");
            settings.AddDefaultTag("Combat.Action.Block");
            settings.AddDefaultTag("Combat.Action.Parry");
            settings.AddDefaultTag("Combat.Action.Dodge");
            settings.AddDefaultTag("Combat.Action.Combo");
            settings.AddDefaultTag("Combat.Action.Critical");
            settings.AddDefaultTag("Combat.Action.Backstab");
            settings.AddDefaultTag("Combat.Action.Headshot");
        }
        
        private static void AddMovementTags(GameplayTagSettings settings)
        {
            // Movement Types
            settings.AddDefaultTag("Movement.Ground");
            settings.AddDefaultTag("Movement.Air");
            settings.AddDefaultTag("Movement.Water");
            settings.AddDefaultTag("Movement.Wall");
            settings.AddDefaultTag("Movement.Ceiling");
            
            // Movement Modifiers
            settings.AddDefaultTag("Movement.Modifier.Slow");
            settings.AddDefaultTag("Movement.Modifier.Fast");
            settings.AddDefaultTag("Movement.Modifier.Frozen");
            settings.AddDefaultTag("Movement.Modifier.Teleport");
            settings.AddDefaultTag("Movement.Modifier.Knockback");
            settings.AddDefaultTag("Movement.Modifier.Gravity");
            settings.AddDefaultTag("Movement.Modifier.NoGravity");
        }
        
        private static void AddStatusEffectTags(GameplayTagSettings settings)
        {
            // Positive Effects
            settings.AddDefaultTag("StatusEffect.Positive.Heal");
            settings.AddDefaultTag("StatusEffect.Positive.Regeneration");
            settings.AddDefaultTag("StatusEffect.Positive.Strength");
            settings.AddDefaultTag("StatusEffect.Positive.Speed");
            settings.AddDefaultTag("StatusEffect.Positive.Defense");
            settings.AddDefaultTag("StatusEffect.Positive.Invincibility");
            settings.AddDefaultTag("StatusEffect.Positive.DoubleJump");
            settings.AddDefaultTag("StatusEffect.Positive.Flight");
            
            // Negative Effects
            settings.AddDefaultTag("StatusEffect.Negative.Poison");
            settings.AddDefaultTag("StatusEffect.Negative.Burn");
            settings.AddDefaultTag("StatusEffect.Negative.Freeze");
            settings.AddDefaultTag("StatusEffect.Negative.Stun");
            settings.AddDefaultTag("StatusEffect.Negative.Slow");
            settings.AddDefaultTag("StatusEffect.Negative.Weakness");
            settings.AddDefaultTag("StatusEffect.Negative.Blind");
            settings.AddDefaultTag("StatusEffect.Negative.Silence");
            settings.AddDefaultTag("StatusEffect.Negative.Confusion");
            settings.AddDefaultTag("StatusEffect.Negative.Fear");
        }
        
        private static void AddItemTags(GameplayTagSettings settings)
        {
            // Item Categories
            settings.AddDefaultTag("Item.Category.Consumable");
            settings.AddDefaultTag("Item.Category.Equipment");
            settings.AddDefaultTag("Item.Category.Key");
            settings.AddDefaultTag("Item.Category.Currency");
            settings.AddDefaultTag("Item.Category.Quest");
            settings.AddDefaultTag("Item.Category.Collectible");
            
            // Item Rarity
            settings.AddDefaultTag("Item.Rarity.Common");
            settings.AddDefaultTag("Item.Rarity.Uncommon");
            settings.AddDefaultTag("Item.Rarity.Rare");
            settings.AddDefaultTag("Item.Rarity.Epic");
            settings.AddDefaultTag("Item.Rarity.Legendary");
            settings.AddDefaultTag("Item.Rarity.Artifact");
            
            // Equipment Slots
            settings.AddDefaultTag("Item.Equipment.Helmet");
            settings.AddDefaultTag("Item.Equipment.Armor");
            settings.AddDefaultTag("Item.Equipment.Boots");
            settings.AddDefaultTag("Item.Equipment.Gloves");
            settings.AddDefaultTag("Item.Equipment.Ring");
            settings.AddDefaultTag("Item.Equipment.Necklace");
            settings.AddDefaultTag("Item.Equipment.MainHand");
            settings.AddDefaultTag("Item.Equipment.OffHand");
        }
        
        private static void AddEnvironmentTags(GameplayTagSettings settings)
        {
            // Environment Types
            settings.AddDefaultTag("Environment.Forest");
            settings.AddDefaultTag("Environment.Desert");
            settings.AddDefaultTag("Environment.Mountain");
            settings.AddDefaultTag("Environment.Cave");
            settings.AddDefaultTag("Environment.Ocean");
            settings.AddDefaultTag("Environment.City");
            settings.AddDefaultTag("Environment.Dungeon");
            settings.AddDefaultTag("Environment.Space");
            
            // Environmental Hazards
            settings.AddDefaultTag("Environment.Hazard.Lava");
            settings.AddDefaultTag("Environment.Hazard.Spikes");
            settings.AddDefaultTag("Environment.Hazard.Poison");
            settings.AddDefaultTag("Environment.Hazard.Electricity");
            settings.AddDefaultTag("Environment.Hazard.Wind");
            settings.AddDefaultTag("Environment.Hazard.Vacuum");
            
            // Interactive Objects
            settings.AddDefaultTag("Environment.Interactive.Door");
            settings.AddDefaultTag("Environment.Interactive.Lever");
            settings.AddDefaultTag("Environment.Interactive.Button");
            settings.AddDefaultTag("Environment.Interactive.Chest");
            settings.AddDefaultTag("Environment.Interactive.Portal");
            settings.AddDefaultTag("Environment.Interactive.Platform");
        }
        
        private static void AddUITags(GameplayTagSettings settings)
        {
            // UI Screens
            settings.AddDefaultTag("UI.Screen.MainMenu");
            settings.AddDefaultTag("UI.Screen.PauseMenu");
            settings.AddDefaultTag("UI.Screen.Settings");
            settings.AddDefaultTag("UI.Screen.Inventory");
            settings.AddDefaultTag("UI.Screen.Map");
            settings.AddDefaultTag("UI.Screen.Journal");
            settings.AddDefaultTag("UI.Screen.Shop");
            settings.AddDefaultTag("UI.Screen.Crafting");
            settings.AddDefaultTag("UI.Screen.CharacterSheet");
            settings.AddDefaultTag("UI.Screen.GameOver");
            settings.AddDefaultTag("UI.Screen.Victory");
            
            // UI Elements
            settings.AddDefaultTag("UI.Element.HealthBar");
            settings.AddDefaultTag("UI.Element.ManaBar");
            settings.AddDefaultTag("UI.Element.StaminaBar");
            settings.AddDefaultTag("UI.Element.Minimap");
            settings.AddDefaultTag("UI.Element.Crosshair");
            settings.AddDefaultTag("UI.Element.Tooltip");
            settings.AddDefaultTag("UI.Element.Notification");
            settings.AddDefaultTag("UI.Element.DialogBox");
            
            // UI States
            settings.AddDefaultTag("UI.State.Visible");
            settings.AddDefaultTag("UI.State.Hidden");
            settings.AddDefaultTag("UI.State.Animating");
            settings.AddDefaultTag("UI.State.Interactive");
            settings.AddDefaultTag("UI.State.Disabled");
        }
        
        private static void AddAudioTags(GameplayTagSettings settings)
        {
            // Audio Categories
            settings.AddDefaultTag("Audio.Music.Menu");
            settings.AddDefaultTag("Audio.Music.Gameplay");
            settings.AddDefaultTag("Audio.Music.Combat");
            settings.AddDefaultTag("Audio.Music.Victory");
            settings.AddDefaultTag("Audio.Music.GameOver");
            
            // Sound Effects
            settings.AddDefaultTag("Audio.SFX.Jump");
            settings.AddDefaultTag("Audio.SFX.Land");
            settings.AddDefaultTag("Audio.SFX.Footstep");
            settings.AddDefaultTag("Audio.SFX.Attack");
            settings.AddDefaultTag("Audio.SFX.Hit");
            settings.AddDefaultTag("Audio.SFX.Collect");
            settings.AddDefaultTag("Audio.SFX.UI.Click");
            settings.AddDefaultTag("Audio.SFX.UI.Hover");
            settings.AddDefaultTag("Audio.SFX.UI.Open");
            settings.AddDefaultTag("Audio.SFX.UI.Close");
            
            // Voice
            settings.AddDefaultTag("Audio.Voice.Player");
            settings.AddDefaultTag("Audio.Voice.NPC");
            settings.AddDefaultTag("Audio.Voice.Narrator");
            
            // Ambient
            settings.AddDefaultTag("Audio.Ambient.Forest");
            settings.AddDefaultTag("Audio.Ambient.City");
            settings.AddDefaultTag("Audio.Ambient.Cave");
            settings.AddDefaultTag("Audio.Ambient.Ocean");
        }
        
        private static void AddGameStateTags(GameplayTagSettings settings)
        {
            // Game Modes
            settings.AddDefaultTag("GameState.Mode.Singleplayer");
            settings.AddDefaultTag("GameState.Mode.Multiplayer");
            settings.AddDefaultTag("GameState.Mode.Coop");
            settings.AddDefaultTag("GameState.Mode.PvP");
            settings.AddDefaultTag("GameState.Mode.Tutorial");
            settings.AddDefaultTag("GameState.Mode.Sandbox");
            
            // Game Phases
            settings.AddDefaultTag("GameState.Phase.Loading");
            settings.AddDefaultTag("GameState.Phase.Playing");
            settings.AddDefaultTag("GameState.Phase.Paused");
            settings.AddDefaultTag("GameState.Phase.GameOver");
            settings.AddDefaultTag("GameState.Phase.Victory");
            settings.AddDefaultTag("GameState.Phase.Cutscene");
            
            // Save System
            settings.AddDefaultTag("GameState.Save.AutoSave");
            settings.AddDefaultTag("GameState.Save.QuickSave");
            settings.AddDefaultTag("GameState.Save.Checkpoint");
            
            // Difficulty
            settings.AddDefaultTag("GameState.Difficulty.Easy");
            settings.AddDefaultTag("GameState.Difficulty.Normal");
            settings.AddDefaultTag("GameState.Difficulty.Hard");
            settings.AddDefaultTag("GameState.Difficulty.Nightmare");
        }
        
        [MenuItem("Tools/Gameplay Tags/Open Tag Editor", priority = 1)]
        public static void OpenTagEditor()
        {
            GameplayTagEditor.ShowWindow();
        }
        
        [MenuItem("GameObject/Gameplay Tags/Add Tag Manager", priority = 1)]
        public static void CreateTagManager()
        {
            // Check if one already exists
            var existing = Object.FindFirstObjectByType<GameplayTagManager>();
            if (existing != null)
            {
                Selection.activeGameObject = existing.gameObject;
                EditorGUIUtility.PingObject(existing.gameObject);
                Debug.Log("GameplayTagManager already exists in scene");
                return;
            }
            
            // Create new one
            var go = new GameObject("GameplayTagManager");
            var manager = go.AddComponent<GameplayTagManager>();
            
            // Try to find and assign a tag settings asset
            var settingsGuids = AssetDatabase.FindAssets("t:GameplayTagSettings");
            if (settingsGuids.Length > 0)
            {
                var path = AssetDatabase.GUIDToAssetPath(settingsGuids[0]);
                var settings = AssetDatabase.LoadAssetAtPath<GameplayTagSettings>(path);
                manager.TagSettings = settings;
            }
            
            Selection.activeGameObject = go;
            EditorGUIUtility.PingObject(go);
            
            Debug.Log("Created GameplayTagManager in scene");
        }
        
        [MenuItem("Tools/Gameplay Tags/Validate All Tag Settings")]
        public static void ValidateAllTagSettings()
        {
            var settingsGuids = AssetDatabase.FindAssets("t:GameplayTagSettings");
            var totalErrors = 0;
            
            foreach (var guid in settingsGuids)
            {
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var settings = AssetDatabase.LoadAssetAtPath<GameplayTagSettings>(path);
                
                if (settings != null)
                {
                    var errorCount = settings.DefaultTags.Count;
                    settings.ValidateAndSortTags();
                    var newCount = settings.DefaultTags.Count;
                    
                    if (errorCount != newCount)
                    {
                        totalErrors += (errorCount - newCount);
                        Debug.Log($"Cleaned {errorCount - newCount} invalid tags from {path}");
                    }
                }
            }
            
            if (totalErrors > 0)
            {
                Debug.Log($"Validation complete. Fixed {totalErrors} total errors across all tag settings.");
            }
            else
            {
                Debug.Log("All tag settings are valid!");
            }
        }
        
        [MenuItem("Tools/Gameplay Tags/Apply All Settings to Manager")]
        public static void ApplyAllSettingsToManager()
        {
            var manager = GameplayTagManager.Instance;
            if (manager == null)
            {
                Debug.LogError("No GameplayTagManager found in scene");
                return;
            }
            
            var settingsGuids = AssetDatabase.FindAssets("t:GameplayTagSettings");
            var totalTags = 0;
            
            foreach (var guid in settingsGuids)
            {
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var settings = AssetDatabase.LoadAssetAtPath<GameplayTagSettings>(path);
                
                if (settings != null)
                {
                    manager.RegisterTags(settings.DefaultTags);
                    totalTags += settings.DefaultTags.Count;
                }
            }
            
            Debug.Log($"Applied {totalTags} tags to GameplayTagManager from all settings assets");
        }
    }
}
