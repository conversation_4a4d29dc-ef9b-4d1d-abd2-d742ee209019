using UnityEditor;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using UnityEngine;
using GameplayTags.Core;

namespace GameplayTags.Editor
{
    /// <summary>
    /// Build processor to validate gameplay tags during build
    /// </summary>
    public class GameplayTagBuildProcessor : IPreprocessBuildWithReport
    {
        public int callbackOrder => 0;

        public void OnPreprocessBuild(BuildReport report)
        {
            ValidateAllTagSettings();
        }

        private void ValidateAllTagSettings()
        {
            var settingsGuids = AssetDatabase.FindAssets("t:GameplayTagSettings");
            var hasValidationErrors = false;

            foreach (var guid in settingsGuids)
            {
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var settings = AssetDatabase.LoadAssetAtPath<GameplayTagSettings>(path);
                
                if (settings != null && settings.ValidateTagsOnBuild)
                {
                    var errors = ValidateTagSettings(settings);
                    if (errors.Count > 0)
                    {
                        hasValidationErrors = true;
                        Debug.LogError($"Gameplay Tag validation errors in {path}:");
                        foreach (var error in errors)
                        {
                            Debug.LogError($"  - {error}");
                        }
                    }
                }
            }

            if (hasValidationErrors)
            {
                var shouldContinue = EditorUtility.DisplayDialog(
                    "Gameplay Tag Validation Errors",
                    "Some gameplay tag settings have validation errors. Continue with build anyway?",
                    "Continue", "Cancel Build");

                if (!shouldContinue)
                {
                    throw new BuildFailedException("Build cancelled due to gameplay tag validation errors.");
                }
            }
        }

        private System.Collections.Generic.List<string> ValidateTagSettings(GameplayTagSettings settings)
        {
            var errors = new System.Collections.Generic.List<string>();
            var seenTags = new System.Collections.Generic.HashSet<string>();

            foreach (var tag in settings.DefaultTags)
            {
                // Check for empty or whitespace tags
                if (string.IsNullOrWhiteSpace(tag))
                {
                    errors.Add("Empty or whitespace tag found");
                    continue;
                }

                // Check for duplicates
                if (seenTags.Contains(tag))
                {
                    errors.Add($"Duplicate tag: '{tag}'");
                    continue;
                }
                seenTags.Add(tag);

                // Check for invalid characters
                if (tag.Contains(".."))
                {
                    errors.Add($"Tag contains consecutive dots: '{tag}'");
                }

                if (tag.StartsWith(".") || tag.EndsWith("."))
                {
                    errors.Add($"Tag starts or ends with dot: '{tag}'");
                }

                // Check for reserved characters
                var invalidChars = new char[] { ' ', '\t', '\n', '\r', '/', '\\', '*', '?', '<', '>', '|', '"' };
                foreach (var invalidChar in invalidChars)
                {
                    if (tag.Contains(invalidChar))
                    {
                        errors.Add($"Tag contains invalid character '{invalidChar}': '{tag}'");
                        break;
                    }
                }
            }

            return errors;
        }
    }
}
