using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using GameplayTags.Core;

namespace GameplayTags.Editor
{
    /// <summary>
    /// Represents a node in the tag hierarchy tree
    /// </summary>
    public class TagNode
    {
        public string FullPath { get; set; }
        public string Name { get; set; }
        public bool IsActualTag { get; set; }
        public TagNode Parent { get; set; }
        public List<TagNode> Children { get; set; }
    }

    /// <summary>
    /// Editor window for managing gameplay tags
    /// </summary>
    public class GameplayTagEditor : EditorWindow
    {
        private GameplayTagSettings tagSettings;
        private Vector2 scrollPosition;
        private string newTagName = "";
        private bool showHierarchy = true;
        private string searchFilter = "";
        private Dictionary<string, bool> foldoutStates = new();
        
        private GUIStyle headerStyle;
        private GUIStyle tagStyle;
        private GUIStyle parentTagStyle;
        private bool stylesInitialized;
        
        [MenuItem("Window/Gameplay Tags/Tag Editor")]
        public static void ShowWindow()
        {
            var window = GetWindow<GameplayTagEditor>("Gameplay Tag Editor");
            window.minSize = new Vector2(400, 300);
        }
        
        private void OnEnable()
        {
            LoadOrCreateSettings();
        }
        
        private void InitializeStyles()
        {
            if (stylesInitialized) return;
            
            headerStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 16,
                normal = { textColor = EditorGUIUtility.isProSkin ? Color.white : Color.black }
            };
            
            tagStyle = new GUIStyle(EditorStyles.label)
            {
                fontStyle = FontStyle.Bold,
                normal = { textColor = EditorGUIUtility.isProSkin ? new Color(0.9f, 0.9f, 0.9f) : Color.black }
            };
            
            parentTagStyle = new GUIStyle(EditorStyles.label)
            {
                fontStyle = FontStyle.Normal,
                normal = { textColor = EditorGUIUtility.isProSkin ? new Color(0.7f, 0.8f, 1.0f) : new Color(0.2f, 0.3f, 0.8f) }
            };
            
            stylesInitialized = true;
        }
        
        private void OnGUI()
        {
            InitializeStyles();
            
            GUILayout.Space(10);
            
            // Header
            using (new GUILayout.HorizontalScope())
            {
                GUILayout.Label("Gameplay Tag Editor", headerStyle);
                GUILayout.FlexibleSpace();
                
                if (GUILayout.Button("Create New Settings", GUILayout.Width(150)))
                {
                    CreateNewSettings();
                }
            }
            
            GUILayout.Space(10);
            
            // Settings selection
            using (new GUILayout.HorizontalScope())
            {
                GUILayout.Label("Tag Settings Asset:", GUILayout.Width(120));
                var newSettings = (GameplayTagSettings)EditorGUILayout.ObjectField(tagSettings, typeof(GameplayTagSettings), false);
                if (newSettings != tagSettings)
                {
                    tagSettings = newSettings;
                    if (tagSettings != null)
                    {
                        EditorPrefs.SetString("GameplayTagEditor.SettingsPath", AssetDatabase.GetAssetPath(tagSettings));
                    }
                }
            }
            
            if (tagSettings == null)
            {
                EditorGUILayout.HelpBox("Please select or create a GameplayTagSettings asset to begin editing tags.", MessageType.Info);
                return;
            }
            
            GUILayout.Space(10);
            
            // Settings panel
            DrawSettingsPanel();
            
            GUILayout.Space(10);
            
            // Add new tag section
            DrawAddTagSection();
            
            GUILayout.Space(10);
            
            // Filter and view options
            DrawFilterSection();
            
            GUILayout.Space(5);
            
            // Tags list
            DrawTagsList();
            
            GUILayout.Space(10);
            
            // Actions
            DrawActionsSection();
        }
        
        private void DrawSettingsPanel()
        {
            using (new EditorGUILayout.VerticalScope("box"))
            {
                GUILayout.Label("Settings", EditorStyles.boldLabel);
                
                EditorGUI.BeginChangeCheck();
                
                var autoRegister = EditorGUILayout.Toggle("Auto Register on Awake", tagSettings.AutoRegisterOnAwake);
                var validateOnBuild = EditorGUILayout.Toggle("Validate Tags on Build", tagSettings.ValidateTagsOnBuild);
                var allowDynamic = EditorGUILayout.Toggle("Allow Dynamic Registration", tagSettings.AllowDynamicTagRegistration);
                
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RecordObject(tagSettings, "Modify Tag Settings");
                    
                    // Use reflection to set the private fields
                    var autoRegisterField = typeof(GameplayTagSettings).GetField("autoRegisterOnAwake", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    var validateField = typeof(GameplayTagSettings).GetField("validateTagsOnBuild", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    var allowDynamicField = typeof(GameplayTagSettings).GetField("allowDynamicTagRegistration", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    
                    autoRegisterField?.SetValue(tagSettings, autoRegister);
                    validateField?.SetValue(tagSettings, validateOnBuild);
                    allowDynamicField?.SetValue(tagSettings, allowDynamic);
                    
                    EditorUtility.SetDirty(tagSettings);
                }
            }
        }
        
        private void DrawAddTagSection()
        {
            using (new EditorGUILayout.VerticalScope("box"))
            {
                GUILayout.Label("Add New Tag", EditorStyles.boldLabel);
                
                using (new GUILayout.HorizontalScope())
                {
                    GUILayout.Label("Tag Name:", GUILayout.Width(80));
                    newTagName = EditorGUILayout.TextField(newTagName);
                    
                    using (new EditorGUI.DisabledScope(string.IsNullOrWhiteSpace(newTagName)))
                    {
                        if (GUILayout.Button("Add Tag", GUILayout.Width(80)))
                        {
                            AddNewTag();
                        }
                    }
                }
                
                if (!string.IsNullOrEmpty(newTagName))
                {
                    // Show preview of what the tag would look like
                    var validatedName = ValidateTagName(newTagName);
                    if (!string.IsNullOrEmpty(validatedName))
                    {
                        if (tagSettings.DefaultTags.Contains(validatedName))
                        {
                            EditorGUILayout.HelpBox($"Tag '{validatedName}' already exists.", MessageType.Warning);
                        }
                        else
                        {
                            EditorGUILayout.HelpBox($"Will add: '{validatedName}'", MessageType.Info);
                        }
                    }
                    else
                    {
                        EditorGUILayout.HelpBox("Invalid tag name. Tags should be dot-separated (e.g., 'Ability.Fire.Fireball')", MessageType.Error);
                    }
                }
            }
        }
        
        private void DrawFilterSection()
        {
            using (new GUILayout.HorizontalScope())
            {
                GUILayout.Label("Search:", GUILayout.Width(60));
                searchFilter = EditorGUILayout.TextField(searchFilter);
                
                GUILayout.Space(10);
                
                showHierarchy = EditorGUILayout.Toggle("Show Hierarchy", showHierarchy, GUILayout.Width(120));
                
                if (GUILayout.Button("Clear Filter", GUILayout.Width(80)))
                {
                    searchFilter = "";
                    GUI.FocusControl(null);
                }
            }
            
            // Hierarchy controls
            if (showHierarchy)
            {
                using (new GUILayout.HorizontalScope())
                {
                    GUILayout.FlexibleSpace();
                    
                    if (GUILayout.Button("Expand All", EditorStyles.miniButton, GUILayout.Width(80)))
                    {
                        ExpandCollapseAll(true);
                    }
                    
                    if (GUILayout.Button("Collapse All", EditorStyles.miniButton, GUILayout.Width(80)))
                    {
                        ExpandCollapseAll(false);
                    }
                }
            }
        }
        
        private void ExpandCollapseAll(bool expand)
        {
            // Clear all foldout states and set them to the desired state
            foldoutStates.Clear();
            
            if (expand)
            {
                // When expanding, we need to set all parent nodes to expanded
                var tags = tagSettings.DefaultTags;
                var allParentPaths = new HashSet<string>();
                
                foreach (var tag in tags)
                {
                    var parts = tag.Split('.');
                    for (var i = 1; i < parts.Length; i++)
                    {
                        var parentPath = string.Join(".", parts, 0, i);
                        allParentPaths.Add(parentPath);
                    }
                }
                
                foreach (var parentPath in allParentPaths)
                {
                    foldoutStates[parentPath] = true;
                }
            }
            // When collapsing, foldoutStates is already cleared, so everything will be collapsed
            
            // Force the editor window to repaint to show the changes
            Repaint();
        }
        
        private void DrawTagsList()
        {
            using (var scrollScope = new EditorGUILayout.ScrollViewScope(scrollPosition))
            {
                scrollPosition = scrollScope.scrollPosition;
                
                var tags = GetFilteredTags();
                
                if (tags.Count == 0)
                {
                    if (string.IsNullOrEmpty(searchFilter))
                    {
                        EditorGUILayout.HelpBox("No tags defined. Add some tags to get started!", MessageType.Info);
                    }
                    else
                    {
                        EditorGUILayout.HelpBox($"No tags match filter '{searchFilter}'", MessageType.Info);
                    }
                    return;
                }
                
                if (showHierarchy)
                {
                    // Show hierarchy stats
                    using (new EditorGUILayout.HorizontalScope())
                    {
                        GUILayout.Label($"Tags: {tags.Count}", EditorStyles.miniLabel);
                        GUILayout.FlexibleSpace();
                        if (!string.IsNullOrEmpty(searchFilter))
                        {
                            GUILayout.Label($"Filtered from {tagSettings.DefaultTags.Count} total", EditorStyles.miniLabel);
                        }
                    }
                    
                    EditorGUILayout.Space(5);
                    DrawHierarchicalTagsList(tags);
                }
                else
                {
                    DrawFlatTagsList(tags);
                }
            }
        }
        
        private void DrawHierarchicalTagsList(List<string> tags)
        {
            // Build a proper tree structure
            var tree = BuildTagTree(tags);
            
            // Draw root nodes
            foreach (var rootNode in tree.Values.Where(node => node.Parent == null).OrderBy(node => node.Name))
            {
                DrawTagNode(rootNode, 0);
            }
        }
        
        private Dictionary<string, TagNode> BuildTagTree(List<string> tags)
        {
            var allNodes = new Dictionary<string, TagNode>();
            
            // First, create all possible nodes (including parent nodes that might not be actual tags)
            var allPossibleTags = new HashSet<string>();
            
            foreach (var tag in tags)
            {
                allPossibleTags.Add(tag);
                
                // Add all parent paths
                var parts = tag.Split('.');
                for (var i = 1; i < parts.Length; i++)
                {
                    var parentPath = string.Join(".", parts, 0, i);
                    allPossibleTags.Add(parentPath);
                }
            }
            
            // Create nodes for all tags
            foreach (var tagPath in allPossibleTags)
            {
                if (!allNodes.ContainsKey(tagPath))
                {
                    var parts = tagPath.Split('.');
                    var name = parts[parts.Length - 1];
                    var isActualTag = tags.Contains(tagPath);
                    
                    allNodes[tagPath] = new TagNode
                    {
                        FullPath = tagPath,
                        Name = name,
                        IsActualTag = isActualTag,
                        Children = new List<TagNode>()
                    };
                }
            }
            
            // Build parent-child relationships
            foreach (var node in allNodes.Values)
            {
                if (node.FullPath.Contains('.'))
                {
                    var parentPath = GetParentPath(node.FullPath);
                    if (allNodes.ContainsKey(parentPath))
                    {
                        node.Parent = allNodes[parentPath];
                        allNodes[parentPath].Children.Add(node);
                    }
                }
            }
            
            return allNodes;
        }
        
        private string GetParentPath(string fullPath)
        {
            var lastDotIndex = fullPath.LastIndexOf('.');
            return lastDotIndex > 0 ? fullPath.Substring(0, lastDotIndex) : "";
        }
        
        private void DrawTagNode(TagNode node, int depth)
        {
            var indentLevel = depth * 20;
            var hasChildren = node.Children.Count > 0;
            
            using (new GUILayout.HorizontalScope())
            {
                GUILayout.Space(indentLevel);
                
                // Draw foldout for nodes with children
                if (hasChildren)
                {
                    if (!foldoutStates.ContainsKey(node.FullPath))
                        foldoutStates[node.FullPath] = false; // Default to collapsed instead of expanded
                    
                    var foldoutRect = GUILayoutUtility.GetRect(16, EditorGUIUtility.singleLineHeight, GUILayout.Width(16));
                    foldoutStates[node.FullPath] = EditorGUI.Foldout(
                        foldoutRect,
                        foldoutStates[node.FullPath], 
                        "",
                        true);
                }
                else
                {
                    GUILayout.Space(16);
                }
                
                // Draw icon based on node type
                var icon = GetNodeIcon(node, hasChildren);
                if (!string.IsNullOrEmpty(icon))
                {
                    GUILayout.Label(icon, GUILayout.Width(16));
                }
                
                // Draw the tag name with appropriate styling
                var style = GetNodeStyle(node);
                var color = GetNodeColor(node);
                
                var originalColor = GUI.color;
                GUI.color = color;
                
                GUILayout.Label(node.Name, style);
                
                GUI.color = originalColor;
                
                // Show full path as tooltip on hover
                var lastRect = GUILayoutUtility.GetLastRect();
                if (lastRect.Contains(Event.current.mousePosition))
                {
                    EditorGUI.LabelField(lastRect, new GUIContent("", node.FullPath));
                }
                
                GUILayout.FlexibleSpace();
                
                // Show tag count for parent nodes
                if (!node.IsActualTag && hasChildren)
                {
                    var tagCount = CountActualTagsInSubtree(node);
                    GUILayout.Label($"({tagCount})", EditorStyles.miniLabel, GUILayout.Width(30));
                }
                
                // Remove button (only for actual tags)
                if (node.IsActualTag)
                {
                    GUI.color = Color.red;
                    if (GUILayout.Button("×", GUILayout.Width(20), GUILayout.Height(16)))
                    {
                        RemoveTag(node.FullPath);
                        return; // Exit early since we're modifying the tree
                    }
                    GUI.color = originalColor;
                }
            }
            
            // Draw children if expanded
            if (hasChildren && foldoutStates.ContainsKey(node.FullPath) && foldoutStates[node.FullPath])
            {
                foreach (var child in node.Children.OrderBy(c => c.Name))
                {
                    DrawTagNode(child, depth + 1);
                }
            }
        }
        
        private string GetNodeIcon(TagNode node, bool hasChildren)
        {
            if (hasChildren)
            {
                return node.IsActualTag ? "📁" : "📂"; // Folder icons
            }
            else
            {
                return node.IsActualTag ? "🏷️" : ""; // Tag icon
            }
        }
        
        private GUIStyle GetNodeStyle(TagNode node)
        {
            if (node.IsActualTag)
            {
                return tagStyle; // Bold for actual tags
            }
            else
            {
                return parentTagStyle; // Different style for parent nodes
            }
        }
        
        private Color GetNodeColor(TagNode node)
        {
            if (node.IsActualTag)
            {
                return EditorGUIUtility.isProSkin ? Color.white : Color.black;
            }
            else
            {
                return EditorGUIUtility.isProSkin ? new Color(0.7f, 0.7f, 0.7f) : new Color(0.4f, 0.4f, 0.4f);
            }
        }
        
        private int CountActualTagsInSubtree(TagNode node)
        {
            var count = node.IsActualTag ? 1 : 0;
            foreach (var child in node.Children)
            {
                count += CountActualTagsInSubtree(child);
            }
            return count;
        }
        
        private void DrawFlatTagsList(List<string> tags)
        {
            foreach (var tag in tags.OrderBy(t => t))
            {
                using (new GUILayout.HorizontalScope())
                {
                    GUILayout.Label(tag, tagStyle);
                    GUILayout.FlexibleSpace();
                    
                    if (GUILayout.Button("×", GUILayout.Width(20), GUILayout.Height(16)))
                    {
                        RemoveTag(tag);
                    }
                }
            }
        }
        
        private void DrawActionsSection()
        {
            using (new EditorGUILayout.VerticalScope("box"))
            {
                GUILayout.Label("Actions", EditorStyles.boldLabel);
                
                using (new GUILayout.HorizontalScope())
                {
                    if (GUILayout.Button("Validate & Sort Tags"))
                    {
                        Undo.RecordObject(tagSettings, "Validate and Sort Tags");
                        tagSettings.ValidateAndSortTags();
                    }
                    
                    if (GUILayout.Button("Clear All Tags"))
                    {
                        if (EditorUtility.DisplayDialog("Clear All Tags", 
                            "Are you sure you want to remove all tags? This action cannot be undone.", 
                            "Yes", "Cancel"))
                        {
                            Undo.RecordObject(tagSettings, "Clear All Tags");
                            tagSettings.ClearDefaultTags();
                            EditorUtility.SetDirty(tagSettings);
                        }
                    }
                    
                    if (GUILayout.Button("Apply to Manager"))
                    {
                        ApplyToManager();
                    }
                }
                
                EditorGUILayout.HelpBox(
                    "• Validate & Sort: Removes duplicates and sorts alphabetically\n" +
                    "• Clear All: Removes all tags from this settings asset\n" +
                    "• Apply to Manager: Registers all tags with the GameplayTagManager", 
                    MessageType.Info);
            }
        }
        
        private void LoadOrCreateSettings()
        {
            // Try to load from EditorPrefs
            var settingsPath = EditorPrefs.GetString("GameplayTagEditor.SettingsPath", "");
            if (!string.IsNullOrEmpty(settingsPath))
            {
                tagSettings = AssetDatabase.LoadAssetAtPath<GameplayTagSettings>(settingsPath);
            }
            
            // If still null, try to find any existing settings
            if (tagSettings == null)
            {
                var guids = AssetDatabase.FindAssets("t:GameplayTagSettings");
                if (guids.Length > 0)
                {
                    var path = AssetDatabase.GUIDToAssetPath(guids[0]);
                    tagSettings = AssetDatabase.LoadAssetAtPath<GameplayTagSettings>(path);
                    EditorPrefs.SetString("GameplayTagEditor.SettingsPath", path);
                }
            }
        }
        
        private void CreateNewSettings()
        {
            var path = EditorUtility.SaveFilePanelInProject(
                "Create Gameplay Tag Settings",
                "GameplayTagSettings",
                "asset",
                "Choose where to save the new Gameplay Tag Settings asset");
                
            if (!string.IsNullOrEmpty(path))
            {
                var settings = CreateInstance<GameplayTagSettings>();
                AssetDatabase.CreateAsset(settings, path);
                AssetDatabase.SaveAssets();
                
                tagSettings = settings;
                EditorPrefs.SetString("GameplayTagEditor.SettingsPath", path);
                
                Selection.activeObject = settings;
            }
        }
        
        private void AddNewTag()
        {
            var validatedName = ValidateTagName(newTagName);
            if (!string.IsNullOrEmpty(validatedName))
            {
                Undo.RecordObject(tagSettings, "Add Tag");
                if (tagSettings.AddDefaultTag(validatedName))
                {
                    EditorUtility.SetDirty(tagSettings);
                    newTagName = "";
                    GUI.FocusControl(null);
                }
            }
        }
        
        private void RemoveTag(string tagName)
        {
            Undo.RecordObject(tagSettings, "Remove Tag");
            tagSettings.RemoveDefaultTag(tagName);
            EditorUtility.SetDirty(tagSettings);
        }
        
        private void ApplyToManager()
        {
            var manager = GameplayTagManager.Instance;
            if (manager != null)
            {
                manager.RegisterTags(tagSettings.DefaultTags);
                Debug.Log($"Applied {tagSettings.DefaultTags.Count} tags to GameplayTagManager");
            }
            else
            {
                Debug.LogWarning("GameplayTagManager not found in scene");
            }
        }
        
        private List<string> GetFilteredTags()
        {
            var tags = tagSettings.DefaultTags;
            
            if (string.IsNullOrEmpty(searchFilter))
                return tags.ToList();
            
            var filteredTags = new HashSet<string>();
            var filter = searchFilter.ToLower();
            
            // Add tags that directly match the filter
            foreach (var tag in tags)
            {
                if (tag.ToLower().Contains(filter))
                {
                    filteredTags.Add(tag);
                    
                    // Also add all parent paths for hierarchy display
                    var parts = tag.Split('.');
                    for (var i = 1; i < parts.Length; i++)
                    {
                        var parentPath = string.Join(".", parts, 0, i);
                        if (tags.Contains(parentPath))
                        {
                            filteredTags.Add(parentPath);
                        }
                    }
                }
            }
            
            // Also include parents of matching tags for better hierarchy display
            var tagsToAdd = new HashSet<string>();
            foreach (var tag in filteredTags.ToList())
            {
                var parts = tag.Split('.');
                for (var i = 1; i < parts.Length; i++)
                {
                    var parentPath = string.Join(".", parts, 0, i);
                    if (tags.Contains(parentPath))
                    {
                        tagsToAdd.Add(parentPath);
                    }
                }
            }
            
            filteredTags.UnionWith(tagsToAdd);
            return filteredTags.ToList();
        }
        
        private string ValidateTagName(string tagName)
        {
            if (string.IsNullOrWhiteSpace(tagName)) return null;
            
            // Remove leading/trailing whitespace and dots
            tagName = tagName.Trim().Trim('.');
            
            if (string.IsNullOrEmpty(tagName)) return null;
            
            // Replace multiple consecutive dots with single dots
            while (tagName.Contains(".."))
            {
                tagName = tagName.Replace("..", ".");
            }
            
            return tagName;
        }
        
        private string GetTagDisplayName(string fullTagName)
        {
            var lastDotIndex = fullTagName.LastIndexOf('.');
            return lastDotIndex >= 0 ? fullTagName.Substring(lastDotIndex + 1) : fullTagName;
        }
    }
}
